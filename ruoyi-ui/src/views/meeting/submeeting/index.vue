<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="父会议" prop="parentMeetingId">
        <el-select v-model="queryParams.parentMeetingId" placeholder="请选择父会议" clearable>
          <el-option
            v-for="meeting in meetingOptions"
            :key="meeting.meetingId"
            :label="meeting.meetingName"
            :value="meeting.meetingId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="子会议名称" prop="subMeetingName">
        <el-input
          v-model="queryParams.subMeetingName"
          placeholder="请输入子会议名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="未开始" value="0" />
          <el-option label="进行中" value="1" />
          <el-option label="已结束" value="2" />
          <el-option label="已取消" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['meeting:submeeting:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['meeting:submeeting:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['meeting:submeeting:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['meeting:submeeting:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="subMeetingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="父会议名称" align="center" prop="parentMeetingName" />
      <el-table-column label="子会议名称" align="center" prop="subMeetingName" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会议地点" align="center" prop="location" />
      <el-table-column label="组织者" align="center" prop="organizer" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '0'" type="info">未开始</el-tag>
          <el-tag v-else-if="scope.row.status == '1'" type="success">进行中</el-tag>
          <el-tag v-else-if="scope.row.status == '2'" type="warning">已结束</el-tag>
          <el-tag v-else-if="scope.row.status == '3'" type="danger">已取消</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['meeting:submeeting:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="handleParticipants(scope.row)"
            v-hasPermi="['meeting:submeeting:edit']"
          >人员管理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleAgenda(scope.row)"
            v-hasPermi="['meeting:agenda:list']"
          >议题管理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['meeting:submeeting:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改子会议对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="父会议" prop="parentMeetingId">
              <el-select v-model="form.parentMeetingId" placeholder="请选择父会议">
                <el-option
                  v-for="meeting in meetingOptions"
                  :key="meeting.meetingId"
                  :label="meeting.meetingName"
                  :value="meeting.meetingId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="子会议名称" prop="subMeetingName">
              <el-input v-model="form.subMeetingName" placeholder="请输入子会议名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会议地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入会议地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织者" prop="organizer">
              <el-input v-model="form.organizer" placeholder="请输入组织者" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="子会议描述" prop="subMeetingDesc">
              <el-input v-model="form.subMeetingDesc" type="textarea" placeholder="请输入子会议描述"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSubMeeting, getSubMeeting, delSubMeeting, addSubMeeting, updateSubMeeting } from "@/api/meeting/submeeting";
import { listMeeting } from "@/api/meeting/meeting";

export default {
  name: "SubMeeting",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 子会议表格数据
      subMeetingList: [],
      // 会议选项
      meetingOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentMeetingId: null,
        subMeetingName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        parentMeetingId: [
          { required: true, message: "父会议不能为空", trigger: "change" }
        ],
        subMeetingName: [
          { required: true, message: "子会议名称不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getMeetingOptions();
  },
  methods: {
    /** 查询子会议列表 */
    getList() {
      this.loading = true;
      listSubMeeting(this.queryParams).then(response => {
        this.subMeetingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取会议选项 */
    getMeetingOptions() {
      listMeeting().then(response => {
        this.meetingOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        subMeetingId: null,
        parentMeetingId: null,
        subMeetingName: null,
        subMeetingDesc: null,
        startTime: null,
        endTime: null,
        location: null,
        organizer: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.subMeetingId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加子会议";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const subMeetingId = row.subMeetingId || this.ids
      getSubMeeting(subMeetingId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改子会议";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.subMeetingId != null) {
            updateSubMeeting(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSubMeeting(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const subMeetingIds = row.subMeetingId || this.ids;
      this.$modal.confirm('是否确认删除子会议编号为"' + subMeetingIds + '"的数据项？').then(function() {
        return delSubMeeting(subMeetingIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('meeting/submeeting/export', {
        ...this.queryParams
      }, `submeeting_${new Date().getTime()}.xlsx`)
    },
    /** 人员管理 */
    handleParticipants(row) {
      this.$router.push({
        path: '/meeting/participants',
        query: { subMeetingId: row.subMeetingId, subMeetingName: row.subMeetingName }
      });
    },
    /** 议题管理 */
    handleAgenda(row) {
      this.$router.push({
        path: '/meeting/agenda',
        query: { subMeetingId: row.subMeetingId, subMeetingName: row.subMeetingName }
      });
    }
  }
};
</script>
