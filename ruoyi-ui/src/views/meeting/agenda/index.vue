<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="子会议" prop="subMeetingId">
        <el-select v-model="queryParams.subMeetingId" placeholder="请选择子会议" clearable>
          <el-option
            v-for="meeting in subMeetingOptions"
            :key="meeting.subMeetingId"
            :label="meeting.subMeetingName"
            :value="meeting.subMeetingId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="议题标题" prop="agendaTitle">
        <el-input
          v-model="queryParams.agendaTitle"
          placeholder="请输入议题标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="汇报人" prop="presenter">
        <el-input
          v-model="queryParams.presenter"
          placeholder="请输入汇报人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="待讨论" value="0" />
          <el-option label="讨论中" value="1" />
          <el-option label="已完成" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['meeting:agenda:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['meeting:agenda:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['meeting:agenda:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['meeting:agenda:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="agendaList" @selection-change="handleSelectionChange" row-key="agendaId">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="排序" align="center" prop="orderNum" width="80" />
      <el-table-column label="子会议名称" align="center" prop="subMeetingName" />
      <el-table-column label="议题标题" align="center" prop="agendaTitle" />
      <el-table-column label="汇报人" align="center" prop="presenter" />
      <el-table-column label="预计时长" align="center" prop="duration" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.duration }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '0'" type="info">待讨论</el-tag>
          <el-tag v-else-if="scope.row.status == '1'" type="warning">讨论中</el-tag>
          <el-tag v-else-if="scope.row.status == '2'" type="success">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['meeting:agenda:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-top"
            @click="handleMoveUp(scope.row)"
            v-hasPermi="['meeting:agenda:edit']"
          >上移</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-bottom"
            @click="handleMoveDown(scope.row)"
            v-hasPermi="['meeting:agenda:edit']"
          >下移</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleFiles(scope.row)"
            v-hasPermi="['meeting:file:list']"
          >文件</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-check"
            @click="handleCreateVote(scope.row)"
            v-hasPermi="['meeting:vote:add']"
          >创建投票</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['meeting:agenda:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改议题对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="子会议" prop="subMeetingId">
              <el-select v-model="form.subMeetingId" placeholder="请选择子会议">
                <el-option
                  v-for="meeting in subMeetingOptions"
                  :key="meeting.subMeetingId"
                  :label="meeting.subMeetingName"
                  :value="meeting.subMeetingId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="议题标题" prop="agendaTitle">
              <el-input v-model="form.agendaTitle" placeholder="请输入议题标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="汇报人" prop="presenter">
              <el-input v-model="form.presenter" placeholder="请输入汇报人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计时长" prop="duration">
              <el-input-number v-model="form.duration" :min="1" :max="999" controls-position="right" />
              <span style="margin-left: 10px;">分钟</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序号" prop="orderNum">
              <el-input-number v-model="form.orderNum" :min="1" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="待讨论" value="0" />
                <el-option label="讨论中" value="1" />
                <el-option label="已完成" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="议题内容" prop="agendaContent">
              <el-input v-model="form.agendaContent" type="textarea" :rows="4" placeholder="请输入议题内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAgenda, getAgenda, delAgenda, addAgenda, updateAgenda, moveUpAgenda, moveDownAgenda, createVoteForAgenda } from "@/api/meeting/agenda";
import { listSubMeeting } from "@/api/meeting/submeeting";

export default {
  name: "Agenda",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 议题表格数据
      agendaList: [],
      // 子会议选项
      subMeetingOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        subMeetingId: null,
        agendaTitle: null,
        presenter: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        subMeetingId: [
          { required: true, message: "子会议不能为空", trigger: "change" }
        ],
        agendaTitle: [
          { required: true, message: "议题标题不能为空", trigger: "blur" }
        ],
        presenter: [
          { required: true, message: "汇报人不能为空", trigger: "blur" }
        ],
        duration: [
          { required: true, message: "预计时长不能为空", trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: "排序号不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getSubMeetingOptions();
  },
  methods: {
    /** 查询议题列表 */
    getList() {
      this.loading = true;
      listAgenda(this.queryParams).then(response => {
        this.agendaList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取子会议选项 */
    getSubMeetingOptions() {
      listSubMeeting().then(response => {
        this.subMeetingOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        agendaId: null,
        subMeetingId: null,
        agendaTitle: null,
        agendaContent: null,
        presenter: null,
        duration: 30,
        orderNum: 1,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.agendaId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加议题";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const agendaId = row.agendaId || this.ids
      getAgenda(agendaId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改议题";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.agendaId != null) {
            updateAgenda(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAgenda(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const agendaIds = row.agendaId || this.ids;
      this.$modal.confirm('是否确认删除议题编号为"' + agendaIds + '"的数据项？').then(function() {
        return delAgenda(agendaIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('meeting/agenda/export', {
        ...this.queryParams
      }, `agenda_${new Date().getTime()}.xlsx`)
    },
    /** 上移议题 */
    handleMoveUp(row) {
      moveUpAgenda(row.agendaId).then(response => {
        this.$modal.msgSuccess("上移成功");
        this.getList();
      });
    },
    /** 下移议题 */
    handleMoveDown(row) {
      moveDownAgenda(row.agendaId).then(response => {
        this.$modal.msgSuccess("下移成功");
        this.getList();
      });
    },
    /** 文件管理 */
    handleFiles(row) {
      this.$router.push({
        path: '/meeting/file',
        query: { agendaId: row.agendaId, agendaTitle: row.agendaTitle }
      });
    },
    /** 创建投票 */
    handleCreateVote(row) {
      this.$modal.confirm('是否为议题"' + row.agendaTitle + '"创建投票？').then(function() {
        return createVoteForAgenda(row.agendaId);
      }).then(() => {
        this.$modal.msgSuccess("投票创建成功");
      }).catch(() => {});
    }
  }
};
</script>
