<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文件名称" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="议题" prop="agendaId">
        <el-select v-model="queryParams.agendaId" placeholder="请选择议题" clearable>
          <el-option
            v-for="agenda in agendaOptions"
            :key="agenda.agendaId"
            :label="agenda.agendaTitle"
            :value="agenda.agendaId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-select v-model="queryParams.fileType" placeholder="请选择文件类型" clearable>
          <el-option label="PDF" value="application/pdf" />
          <el-option label="Word" value="application/msword" />
          <el-option label="Excel" value="application/vnd.ms-excel" />
          <el-option label="PPT" value="application/vnd.ms-powerpoint" />
          <el-option label="图片" value="image" />
          <el-option label="视频" value="video" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="handleUpload"
          v-hasPermi="['meeting:file:add']"
        >上传文件</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleBatchUpload"
          v-hasPermi="['meeting:file:add']"
        >批量上传</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-share"
          size="mini"
          :disabled="multiple"
          @click="handleDistribute"
          v-hasPermi="['meeting:file:edit']"
        >分发文件</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-s-promotion"
          size="mini"
          :disabled="multiple"
          @click="handleOneClickDistribute"
          v-hasPermi="['meeting:file:edit']"
        >一键分发</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['meeting:file:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fileList" @selection-change="handleSelectionChange" row-key="fileId">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="排序" align="center" prop="orderNum" width="80" />
      <el-table-column label="文件名称" align="center" prop="fileName" min-width="200" />
      <el-table-column label="原始名称" align="center" prop="originalName" min-width="200" />
      <el-table-column label="关联议题" align="center" prop="agendaTitle" />
      <el-table-column label="文件大小" align="center" prop="fileSize" width="100">
        <template slot-scope="scope">
          <span>{{ formatFileSize(scope.row.fileSize) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件类型" align="center" prop="fileExt" width="80" />
      <el-table-column label="是否加密" align="center" prop="isPassword" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isPassword == '1'" type="warning">已加密</el-tag>
          <el-tag v-else type="success">未加密</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '0'" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status == '1'" type="info">已分发</el-tag>
          <el-tag v-else-if="scope.row.status == '2'" type="warning">已收回</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handlePreview(scope.row)"
            v-hasPermi="['meeting:file:query']"
          >预览</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDownload(scope.row)"
            v-hasPermi="['meeting:file:query']"
          >下载</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleRename(scope.row)"
            v-hasPermi="['meeting:file:edit']"
          >重命名</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-share"
            @click="handleDistributeSingle(scope.row)"
            v-hasPermi="['meeting:file:edit']"
          >分发</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['meeting:file:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 文件上传对话框 -->
    <el-dialog title="文件上传" :visible.sync="uploadOpen" width="600px" append-to-body>
      <el-form ref="uploadForm" :model="uploadForm" :rules="uploadRules" label-width="80px">
        <el-form-item label="关联议题" prop="agendaId">
          <el-select v-model="uploadForm.agendaId" placeholder="请选择议题">
            <el-option
              v-for="agenda in agendaOptions"
              :key="agenda.agendaId"
              :label="agenda.agendaTitle"
              :value="agenda.agendaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文件上传" prop="file">
          <el-upload
            ref="upload"
            :limit="1"
            :headers="upload.headers"
            :action="upload.url"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              <span>支持多种格式文件上传</span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpload">确 定</el-button>
        <el-button @click="uploadOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 文件重命名对话框 -->
    <el-dialog title="文件重命名" :visible.sync="renameOpen" width="400px" append-to-body>
      <el-form ref="renameForm" :model="renameForm" :rules="renameRules" label-width="80px">
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="renameForm.fileName" placeholder="请输入新的文件名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRename">确 定</el-button>
        <el-button @click="renameOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog title="文件预览" :visible.sync="previewOpen" width="80%" append-to-body>
      <div v-if="previewUrl" style="text-align: center;">
        <iframe :src="previewUrl" width="100%" height="600px" frameborder="0"></iframe>
      </div>
      <div v-else style="text-align: center; padding: 50px;">
        <i class="el-icon-document" style="font-size: 48px; color: #ccc;"></i>
        <p>该文件类型不支持在线预览</p>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFile, getFile, delFile, renameFile, previewFile, downloadFile, distributeFile, oneClickDistribute } from "@/api/meeting/file";
import { listAgenda } from "@/api/meeting/agenda";
import { getToken } from "@/utils/auth";

export default {
  name: "File",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文件表格数据
      fileList: [],
      // 议题选项
      agendaOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 上传对话框
      uploadOpen: false,
      // 重命名对话框
      renameOpen: false,
      // 预览对话框
      previewOpen: false,
      // 预览URL
      previewUrl: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileName: null,
        agendaId: null,
        fileType: null
      },
      // 上传表单
      uploadForm: {
        agendaId: null
      },
      // 重命名表单
      renameForm: {
        fileId: null,
        fileName: null
      },
      // 上传参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/meeting/file/upload"
      },
      // 表单校验
      uploadRules: {
        agendaId: [
          { required: true, message: "请选择关联议题", trigger: "change" }
        ]
      },
      renameRules: {
        fileName: [
          { required: true, message: "文件名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getAgendaOptions();
  },
  methods: {
    /** 查询文件列表 */
    getList() {
      this.loading = true;
      listFile(this.queryParams).then(response => {
        this.fileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取议题选项 */
    getAgendaOptions() {
      listAgenda().then(response => {
        this.agendaOptions = response.rows;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.fileId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 上传文件 */
    handleUpload() {
      this.uploadForm = { agendaId: null };
      this.uploadOpen = true;
    },
    /** 批量上传 */
    handleBatchUpload() {
      // 实现批量上传逻辑
      this.$modal.msgInfo("批量上传功能开发中");
    },
    /** 提交上传 */
    submitUpload() {
      this.$refs["uploadForm"].validate(valid => {
        if (valid) {
          this.$refs.upload.submit();
        }
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.uploadOpen = false;
      this.$refs.upload.clearFiles();
      this.$modal.msgSuccess("上传成功");
      this.getList();
    },
    /** 预览文件 */
    handlePreview(row) {
      previewFile(row.fileId).then(response => {
        this.previewUrl = response.data;
        this.previewOpen = true;
      });
    },
    /** 下载文件 */
    handleDownload(row) {
      this.download('/meeting/file/download/' + row.fileId, {}, row.originalName);
    },
    /** 重命名文件 */
    handleRename(row) {
      this.renameForm = {
        fileId: row.fileId,
        fileName: row.fileName
      };
      this.renameOpen = true;
    },
    /** 提交重命名 */
    submitRename() {
      this.$refs["renameForm"].validate(valid => {
        if (valid) {
          renameFile(this.renameForm.fileId, this.renameForm.fileName).then(response => {
            this.$modal.msgSuccess("重命名成功");
            this.renameOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 分发文件 */
    handleDistribute() {
      this.$modal.msgInfo("分发功能开发中");
    },
    /** 单个分发 */
    handleDistributeSingle(row) {
      this.$modal.msgInfo("分发功能开发中");
    },
    /** 一键分发 */
    handleOneClickDistribute() {
      this.$modal.msgInfo("一键分发功能开发中");
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const fileIds = row.fileId || this.ids;
      this.$modal.confirm('是否确认删除文件编号为"' + fileIds + '"的数据项？').then(function() {
        return delFile(fileIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + ' MB';
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
      }
    }
  }
};
</script>
