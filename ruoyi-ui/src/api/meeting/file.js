import request from '@/utils/request'

// 查询文件列表
export function listFile(query) {
  return request({
    url: '/meeting/file/list',
    method: 'get',
    params: query
  })
}

// 根据议题ID查询文件列表
export function listFileByAgenda(agendaId) {
  return request({
    url: '/meeting/file/listByAgenda/' + agendaId,
    method: 'get'
  })
}

// 根据会议ID查询文件列表
export function listFileByMeeting(meetingId) {
  return request({
    url: '/meeting/file/listByMeeting/' + meetingId,
    method: 'get'
  })
}

// 查询文件详细
export function getFile(fileId) {
  return request({
    url: '/meeting/file/' + fileId,
    method: 'get'
  })
}

// 新增文件
export function addFile(data) {
  return request({
    url: '/meeting/file',
    method: 'post',
    data: data
  })
}

// 修改文件
export function updateFile(data) {
  return request({
    url: '/meeting/file',
    method: 'put',
    data: data
  })
}

// 删除文件
export function delFile(fileId) {
  return request({
    url: '/meeting/file/' + fileId,
    method: 'delete'
  })
}

// 上传文件
export function uploadFile(data) {
  return request({
    url: '/meeting/file/upload',
    method: 'post',
    data: data
  })
}

// 批量上传文件
export function batchUploadFile(data) {
  return request({
    url: '/meeting/file/batchUpload',
    method: 'post',
    data: data
  })
}

// 下载文件
export function downloadFile(fileId) {
  return request({
    url: '/meeting/file/download/' + fileId,
    method: 'get',
    responseType: 'blob'
  })
}

// 在线预览文件
export function previewFile(fileId, password) {
  return request({
    url: '/meeting/file/preview/' + fileId,
    method: 'get',
    params: {
      password: password
    }
  })
}

// 分发文件
export function distributeFile(fileIds, userIds) {
  return request({
    url: '/meeting/file/distribute',
    method: 'post',
    params: {
      fileIds: fileIds,
      userIds: userIds
    }
  })
}

// 一键分发文件
export function oneClickDistribute(fileIds, meetingId) {
  return request({
    url: '/meeting/file/oneClickDistribute',
    method: 'post',
    params: {
      fileIds: fileIds,
      meetingId: meetingId
    }
  })
}

// 收回文件
export function recallFile(fileIds, userIds) {
  return request({
    url: '/meeting/file/recall',
    method: 'post',
    params: {
      fileIds: fileIds,
      userIds: userIds
    }
  })
}

// 绑定议题
export function bindAgenda(fileId, agendaId) {
  return request({
    url: '/meeting/file/bindAgenda',
    method: 'post',
    params: {
      fileId: fileId,
      agendaId: agendaId
    }
  })
}

// 文件排序
export function sortFile(fileIds) {
  return request({
    url: '/meeting/file/sort',
    method: 'post',
    params: {
      fileIds: fileIds
    }
  })
}

// 重命名文件
export function renameFile(fileId, fileName) {
  return request({
    url: '/meeting/file/rename',
    method: 'post',
    params: {
      fileId: fileId,
      fileName: fileName
    }
  })
}

// 查询分发记录
export function getDistributionRecord(query) {
  return request({
    url: '/meeting/file/distributionRecord',
    method: 'get',
    params: query
  })
}

// 查询文件笔记
export function getFileNotes(fileId) {
  return request({
    url: '/meeting/file/notes/' + fileId,
    method: 'get'
  })
}
