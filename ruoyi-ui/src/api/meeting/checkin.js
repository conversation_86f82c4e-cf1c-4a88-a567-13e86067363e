import request from '@/utils/request'

// 查询签到列表
export function listCheckin(query) {
  return request({
    url: '/meeting/checkin/list',
    method: 'get',
    params: query
  })
}

// 根据子会议ID查询签到
export function getCheckinBySubMeeting(subMeetingId) {
  return request({
    url: '/meeting/checkin/getBySubMeeting/' + subMeetingId,
    method: 'get'
  })
}

// 查询签到详细
export function getCheckin(checkinId) {
  return request({
    url: '/meeting/checkin/' + checkinId,
    method: 'get'
  })
}

// 新增签到
export function addCheckin(data) {
  return request({
    url: '/meeting/checkin',
    method: 'post',
    data: data
  })
}

// 修改签到
export function updateCheckin(data) {
  return request({
    url: '/meeting/checkin',
    method: 'put',
    data: data
  })
}

// 删除签到
export function delCheckin(checkinId) {
  return request({
    url: '/meeting/checkin/' + checkinId,
    method: 'delete'
  })
}

// 关联人员
export function associateUsers(checkinId, userIds) {
  return request({
    url: '/meeting/checkin/associateUsers',
    method: 'post',
    params: {
      checkinId: checkinId,
      userIds: userIds
    }
  })
}

// 取消关联
export function cancelAssociation(checkinId) {
  return request({
    url: '/meeting/checkin/cancelAssociation/' + checkinId,
    method: 'post'
  })
}

// 开始签到
export function startCheckin(checkinId) {
  return request({
    url: '/meeting/checkin/start/' + checkinId,
    method: 'post'
  })
}

// 结束签到
export function endCheckin(checkinId) {
  return request({
    url: '/meeting/checkin/end/' + checkinId,
    method: 'post'
  })
}

// 用户签到
export function userCheckin(checkinId, userId, deviceInfo, ipAddress) {
  return request({
    url: '/meeting/checkin/userCheckin',
    method: 'post',
    params: {
      checkinId: checkinId,
      userId: userId,
      deviceInfo: deviceInfo,
      ipAddress: ipAddress
    }
  })
}

// 补签
export function supplementCheckin(checkinId, userId) {
  return request({
    url: '/meeting/checkin/supplement',
    method: 'post',
    params: {
      checkinId: checkinId,
      userId: userId
    }
  })
}

// 查询签到明细
export function getCheckinDetails(checkinId) {
  return request({
    url: '/meeting/checkin/details/' + checkinId,
    method: 'get'
  })
}

// 导出签到结果
export function exportCheckinResult(checkinId) {
  return request({
    url: '/meeting/checkin/exportResult/' + checkinId,
    method: 'post'
  })
}

// 自动创建签到
export function autoCreateCheckin(subMeetingId) {
  return request({
    url: '/meeting/checkin/autoCreate/' + subMeetingId,
    method: 'post'
  })
}
