import request from '@/utils/request'

// 查询投票列表
export function listVote(query) {
  return request({
    url: '/meeting/vote/list',
    method: 'get',
    params: query
  })
}

// 查询投票详细
export function getVote(voteId) {
  return request({
    url: '/meeting/vote/' + voteId,
    method: 'get'
  })
}

// 新增投票
export function addVote(data) {
  return request({
    url: '/meeting/vote',
    method: 'post',
    data: data
  })
}

// 修改投票
export function updateVote(data) {
  return request({
    url: '/meeting/vote',
    method: 'put',
    data: data
  })
}

// 删除投票
export function delVote(voteId) {
  return request({
    url: '/meeting/vote/' + voteId,
    method: 'delete'
  })
}

// 关联人员
export function associateUsers(voteId, userIds) {
  return request({
    url: '/meeting/vote/associateUsers',
    method: 'post',
    params: {
      voteId: voteId,
      userIds: userIds
    }
  })
}

// 取消关联
export function cancelAssociation(voteId) {
  return request({
    url: '/meeting/vote/cancelAssociation/' + voteId,
    method: 'post'
  })
}

// 开始投票
export function startVote(voteId) {
  return request({
    url: '/meeting/vote/start/' + voteId,
    method: 'post'
  })
}

// 结束投票
export function endVote(voteId) {
  return request({
    url: '/meeting/vote/end/' + voteId,
    method: 'post'
  })
}

// 用户投票
export function userVote(voteId, userId, voteOption) {
  return request({
    url: '/meeting/vote/userVote',
    method: 'post',
    params: {
      voteId: voteId,
      userId: userId,
      voteOption: voteOption
    }
  })
}

// 补投
export function supplementVote(voteId, userId, voteOption) {
  return request({
    url: '/meeting/vote/supplement',
    method: 'post',
    params: {
      voteId: voteId,
      userId: userId,
      voteOption: voteOption
    }
  })
}

// 查询投票明细
export function getVoteDetails(voteId) {
  return request({
    url: '/meeting/vote/details/' + voteId,
    method: 'get'
  })
}

// 导出投票结果
export function exportVoteResult(voteId) {
  return request({
    url: '/meeting/vote/exportResult/' + voteId,
    method: 'post'
  })
}
