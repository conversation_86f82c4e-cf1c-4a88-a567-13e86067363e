import request from '@/utils/request'

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/meeting/device/list',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getDevice(deviceId) {
  return request({
    url: '/meeting/device/' + deviceId,
    method: 'get'
  })
}

// 新增设备
export function addDevice(data) {
  return request({
    url: '/meeting/device',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateDevice(data) {
  return request({
    url: '/meeting/device',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delDevice(deviceId) {
  return request({
    url: '/meeting/device/' + deviceId,
    method: 'delete'
  })
}

// 启用设备
export function enableDevice(deviceId) {
  return request({
    url: '/meeting/device/enable/' + deviceId,
    method: 'post'
  })
}

// 禁用设备
export function disableDevice(deviceId) {
  return request({
    url: '/meeting/device/disable/' + deviceId,
    method: 'post'
  })
}

// 绑定用户
export function bindUsers(deviceId, userIds) {
  return request({
    url: '/meeting/device/bindUsers',
    method: 'post',
    params: {
      deviceId: deviceId,
      userIds: userIds
    }
  })
}

// 解绑用户
export function unbindUsers(deviceId, userIds) {
  return request({
    url: '/meeting/device/unbindUsers',
    method: 'post',
    params: {
      deviceId: deviceId,
      userIds: userIds
    }
  })
}

// 批量绑定用户
export function batchBindUsers(deviceIds, userIds) {
  return request({
    url: '/meeting/device/batchBindUsers',
    method: 'post',
    params: {
      deviceIds: deviceIds,
      userIds: userIds
    }
  })
}

// 批量解绑用户
export function batchUnbindUsers(deviceIds, userIds) {
  return request({
    url: '/meeting/device/batchUnbindUsers',
    method: 'post',
    params: {
      deviceIds: deviceIds,
      userIds: userIds
    }
  })
}

// 获取设备绑定的用户
export function getDeviceUsers(deviceId) {
  return request({
    url: '/meeting/device/users/' + deviceId,
    method: 'get'
  })
}

// 检查设备是否可用
export function checkDeviceAvailable(deviceCode, macAddress) {
  return request({
    url: '/meeting/device/checkAvailable',
    method: 'get',
    params: {
      deviceCode: deviceCode,
      macAddress: macAddress
    }
  })
}
