import request from '@/utils/request'

// 查询会议列表
export function listMeeting(query) {
  return request({
    url: '/meeting/meeting/list',
    method: 'get',
    params: query
  })
}

// 查询会议详细
export function getMeeting(meetingId) {
  return request({
    url: '/meeting/meeting/' + meetingId,
    method: 'get'
  })
}

// 新增会议
export function addMeeting(data) {
  return request({
    url: '/meeting/meeting',
    method: 'post',
    data: data
  })
}

// 修改会议
export function updateMeeting(data) {
  return request({
    url: '/meeting/meeting',
    method: 'put',
    data: data
  })
}

// 删除会议
export function delMeeting(meetingId) {
  return request({
    url: '/meeting/meeting/' + meetingId,
    method: 'delete'
  })
}

// 更新会议状态
export function updateMeetingStatus(meetingId, status) {
  return request({
    url: '/meeting/meeting/status',
    method: 'put',
    params: {
      meetingId: meetingId,
      status: status
    }
  })
}

// 添加会议参与人员
export function addMeetingParticipants(meetingId, userIds) {
  return request({
    url: '/meeting/meeting/participants/add',
    method: 'post',
    params: {
      meetingId: meetingId,
      userIds: userIds
    }
  })
}

// 移除会议参与人员
export function removeMeetingParticipants(meetingId, userIds) {
  return request({
    url: '/meeting/meeting/participants/remove',
    method: 'post',
    params: {
      meetingId: meetingId,
      userIds: userIds
    }
  })
}

// 获取会议参与人员
export function getMeetingParticipants(meetingId) {
  return request({
    url: '/meeting/meeting/participants/' + meetingId,
    method: 'get'
  })
}

// 复制上次会议人员
export function copyLastMeetingParticipants(meetingId, lastMeetingId) {
  return request({
    url: '/meeting/meeting/participants/copyLast',
    method: 'post',
    params: {
      meetingId: meetingId,
      lastMeetingId: lastMeetingId
    }
  })
}
