import request from '@/utils/request'

// 查询议题列表
export function listAgenda(query) {
  return request({
    url: '/meeting/agenda/list',
    method: 'get',
    params: query
  })
}

// 根据子会议ID查询议题列表
export function listAgendaBySubMeeting(subMeetingId) {
  return request({
    url: '/meeting/agenda/listBySubMeeting/' + subMeetingId,
    method: 'get'
  })
}

// 查询议题详细
export function getAgenda(agendaId) {
  return request({
    url: '/meeting/agenda/' + agendaId,
    method: 'get'
  })
}

// 新增议题
export function addAgenda(data) {
  return request({
    url: '/meeting/agenda',
    method: 'post',
    data: data
  })
}

// 修改议题
export function updateAgenda(data) {
  return request({
    url: '/meeting/agenda',
    method: 'put',
    data: data
  })
}

// 删除议题
export function delAgenda(agendaId) {
  return request({
    url: '/meeting/agenda/' + agendaId,
    method: 'delete'
  })
}

// 议题排序
export function sortAgenda(agendaIds) {
  return request({
    url: '/meeting/agenda/sort',
    method: 'post',
    params: {
      agendaIds: agendaIds
    }
  })
}

// 上移议题
export function moveUpAgenda(agendaId) {
  return request({
    url: '/meeting/agenda/moveUp/' + agendaId,
    method: 'post'
  })
}

// 下移议题
export function moveDownAgenda(agendaId) {
  return request({
    url: '/meeting/agenda/moveDown/' + agendaId,
    method: 'post'
  })
}

// 自动创建投票
export function createVoteForAgenda(agendaId) {
  return request({
    url: '/meeting/agenda/createVote/' + agendaId,
    method: 'post'
  })
}
