import request from '@/utils/request'

// 查询通知列表
export function listNotice(query) {
  return request({
    url: '/meeting/notice/list',
    method: 'get',
    params: query
  })
}

// 查询通知详细
export function getNotice(noticeId) {
  return request({
    url: '/meeting/notice/' + noticeId,
    method: 'get'
  })
}

// 新增通知
export function addNotice(data) {
  return request({
    url: '/meeting/notice',
    method: 'post',
    data: data
  })
}

// 修改通知
export function updateNotice(data) {
  return request({
    url: '/meeting/notice',
    method: 'put',
    data: data
  })
}

// 删除通知
export function delNotice(noticeId) {
  return request({
    url: '/meeting/notice/' + noticeId,
    method: 'delete'
  })
}

// 分发通知
export function distributeNotice(data) {
  return request({
    url: '/meeting/notice/distribute',
    method: 'post',
    data: data
  })
}

// 查询分发记录
export function getDistributionRecord(query) {
  return request({
    url: '/meeting/notice/distributionRecord',
    method: 'get',
    params: query
  })
}

// 上传附件
export function uploadAttachment(data) {
  return request({
    url: '/meeting/notice/uploadAttachment',
    method: 'post',
    data: data
  })
}

// 下载附件
export function downloadAttachment(filePath) {
  return request({
    url: '/meeting/notice/downloadAttachment',
    method: 'get',
    params: {
      filePath: filePath
    },
    responseType: 'blob'
  })
}
