import request from '@/utils/request'

// 查询子会议列表
export function listSubMeeting(query) {
  return request({
    url: '/meeting/submeeting/list',
    method: 'get',
    params: query
  })
}

// 根据父会议ID查询子会议列表
export function listSubMeetingByParent(parentMeetingId) {
  return request({
    url: '/meeting/submeeting/listByParent/' + parentMeetingId,
    method: 'get'
  })
}

// 查询子会议详细
export function getSubMeeting(subMeetingId) {
  return request({
    url: '/meeting/submeeting/' + subMeetingId,
    method: 'get'
  })
}

// 新增子会议
export function addSubMeeting(data) {
  return request({
    url: '/meeting/submeeting',
    method: 'post',
    data: data
  })
}

// 修改子会议
export function updateSubMeeting(data) {
  return request({
    url: '/meeting/submeeting',
    method: 'put',
    data: data
  })
}

// 删除子会议
export function delSubMeeting(subMeetingId) {
  return request({
    url: '/meeting/submeeting/' + subMeetingId,
    method: 'delete'
  })
}

// 添加子会议参与人员
export function addSubMeetingParticipants(subMeetingId, userIds) {
  return request({
    url: '/meeting/submeeting/participants/add',
    method: 'post',
    params: {
      subMeetingId: subMeetingId,
      userIds: userIds
    }
  })
}

// 移除子会议参与人员
export function removeSubMeetingParticipants(subMeetingId, userIds) {
  return request({
    url: '/meeting/submeeting/participants/remove',
    method: 'post',
    params: {
      subMeetingId: subMeetingId,
      userIds: userIds
    }
  })
}

// 获取子会议参与人员
export function getSubMeetingParticipants(subMeetingId) {
  return request({
    url: '/meeting/submeeting/participants/' + subMeetingId,
    method: 'get'
  })
}
