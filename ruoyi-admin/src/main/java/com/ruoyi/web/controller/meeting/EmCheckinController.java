package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmCheckin;
import com.ruoyi.meeting.service.IEmCheckinService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 签到管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/checkin")
public class EmCheckinController extends BaseController
{
    @Autowired
    private IEmCheckinService emCheckinService;

    /**
     * 查询签到列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmCheckin emCheckin)
    {
        startPage();
        List<EmCheckin> list = emCheckinService.selectEmCheckinList(emCheckin);
        return getDataTable(list);
    }

    /**
     * 根据子会议ID查询签到
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:query')")
    @GetMapping("/getBySubMeeting/{subMeetingId}")
    public AjaxResult getBySubMeeting(@PathVariable Long subMeetingId)
    {
        EmCheckin checkin = emCheckinService.selectEmCheckinBySubMeetingId(subMeetingId);
        return AjaxResult.success(checkin);
    }

    /**
     * 导出签到列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:export')")
    @Log(title = "签到管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmCheckin emCheckin)
    {
        List<EmCheckin> list = emCheckinService.selectEmCheckinList(emCheckin);
        ExcelUtil<EmCheckin> util = new ExcelUtil<EmCheckin>(EmCheckin.class);
        util.exportExcel(response, list, "签到数据");
    }

    /**
     * 获取签到详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:query')")
    @GetMapping(value = "/{checkinId}")
    public AjaxResult getInfo(@PathVariable("checkinId") Long checkinId)
    {
        return AjaxResult.success(emCheckinService.selectEmCheckinByCheckinId(checkinId));
    }

    /**
     * 新增签到
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:add')")
    @Log(title = "签到管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmCheckin emCheckin)
    {
        return toAjax(emCheckinService.insertEmCheckin(emCheckin));
    }

    /**
     * 修改签到
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:edit')")
    @Log(title = "签到管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmCheckin emCheckin)
    {
        return toAjax(emCheckinService.updateEmCheckin(emCheckin));
    }

    /**
     * 删除签到
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:remove')")
    @Log(title = "签到管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{checkinIds}")
    public AjaxResult remove(@PathVariable Long[] checkinIds)
    {
        return toAjax(emCheckinService.deleteEmCheckinByCheckinIds(checkinIds));
    }

    /**
     * 关联人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:edit')")
    @Log(title = "签到管理", businessType = BusinessType.UPDATE)
    @PostMapping("/associateUsers")
    public AjaxResult associateUsers(@RequestParam Long checkinId, @RequestParam Long[] userIds)
    {
        return toAjax(emCheckinService.associateUsers(checkinId, userIds));
    }

    /**
     * 取消关联
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:edit')")
    @Log(title = "签到管理", businessType = BusinessType.UPDATE)
    @PostMapping("/cancelAssociation/{checkinId}")
    public AjaxResult cancelAssociation(@PathVariable Long checkinId)
    {
        return toAjax(emCheckinService.cancelAssociation(checkinId));
    }

    /**
     * 开始签到
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:edit')")
    @Log(title = "签到管理", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{checkinId}")
    public AjaxResult startCheckin(@PathVariable Long checkinId)
    {
        return toAjax(emCheckinService.startCheckin(checkinId));
    }

    /**
     * 结束签到
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:edit')")
    @Log(title = "签到管理", businessType = BusinessType.UPDATE)
    @PostMapping("/end/{checkinId}")
    public AjaxResult endCheckin(@PathVariable Long checkinId)
    {
        return toAjax(emCheckinService.endCheckin(checkinId));
    }

    /**
     * 用户签到
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:edit')")
    @Log(title = "签到管理", businessType = BusinessType.UPDATE)
    @PostMapping("/userCheckin")
    public AjaxResult userCheckin(@RequestParam Long checkinId, 
                                 @RequestParam Long userId,
                                 @RequestParam(required = false) String deviceInfo,
                                 @RequestParam(required = false) String ipAddress)
    {
        return toAjax(emCheckinService.userCheckin(checkinId, userId, deviceInfo, ipAddress));
    }

    /**
     * 补签
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:edit')")
    @Log(title = "签到管理", businessType = BusinessType.UPDATE)
    @PostMapping("/supplement")
    public AjaxResult supplementCheckin(@RequestParam Long checkinId, @RequestParam Long userId)
    {
        return toAjax(emCheckinService.supplementCheckin(checkinId, userId));
    }

    /**
     * 查询签到明细
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:query')")
    @GetMapping("/details/{checkinId}")
    public AjaxResult checkinDetails(@PathVariable Long checkinId)
    {
        List<Object> details = emCheckinService.selectCheckinDetails(checkinId);
        return AjaxResult.success(details);
    }

    /**
     * 导出签到结果
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:export')")
    @Log(title = "签到管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportResult/{checkinId}")
    public AjaxResult exportCheckinResult(@PathVariable Long checkinId)
    {
        String filePath = emCheckinService.exportCheckinResult(checkinId);
        return AjaxResult.success("导出成功", filePath);
    }

    /**
     * 自动创建签到
     */
    @PreAuthorize("@ss.hasPermi('meeting:checkin:add')")
    @Log(title = "签到管理", businessType = BusinessType.INSERT)
    @PostMapping("/autoCreate/{subMeetingId}")
    public AjaxResult autoCreateCheckin(@PathVariable Long subMeetingId)
    {
        return toAjax(emCheckinService.autoCreateCheckin(subMeetingId));
    }
}
