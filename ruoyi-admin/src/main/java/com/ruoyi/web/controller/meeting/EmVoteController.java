package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmVote;
import com.ruoyi.meeting.service.IEmVoteService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 投票管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/vote")
public class EmVoteController extends BaseController
{
    @Autowired
    private IEmVoteService emVoteService;

    /**
     * 查询投票列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmVote emVote)
    {
        startPage();
        List<EmVote> list = emVoteService.selectEmVoteList(emVote);
        return getDataTable(list);
    }

    /**
     * 导出投票列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:export')")
    @Log(title = "投票管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmVote emVote)
    {
        List<EmVote> list = emVoteService.selectEmVoteList(emVote);
        ExcelUtil<EmVote> util = new ExcelUtil<EmVote>(EmVote.class);
        util.exportExcel(response, list, "投票数据");
    }

    /**
     * 获取投票详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:query')")
    @GetMapping(value = "/{voteId}")
    public AjaxResult getInfo(@PathVariable("voteId") Long voteId)
    {
        return AjaxResult.success(emVoteService.selectEmVoteByVoteId(voteId));
    }

    /**
     * 新增投票
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:add')")
    @Log(title = "投票管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmVote emVote)
    {
        return toAjax(emVoteService.insertEmVote(emVote));
    }

    /**
     * 修改投票
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:edit')")
    @Log(title = "投票管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmVote emVote)
    {
        return toAjax(emVoteService.updateEmVote(emVote));
    }

    /**
     * 删除投票
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:remove')")
    @Log(title = "投票管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{voteIds}")
    public AjaxResult remove(@PathVariable Long[] voteIds)
    {
        return toAjax(emVoteService.deleteEmVoteByVoteIds(voteIds));
    }

    /**
     * 关联人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:edit')")
    @Log(title = "投票管理", businessType = BusinessType.UPDATE)
    @PostMapping("/associateUsers")
    public AjaxResult associateUsers(@RequestParam Long voteId, @RequestParam Long[] userIds)
    {
        return toAjax(emVoteService.associateUsers(voteId, userIds));
    }

    /**
     * 取消关联
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:edit')")
    @Log(title = "投票管理", businessType = BusinessType.UPDATE)
    @PostMapping("/cancelAssociation/{voteId}")
    public AjaxResult cancelAssociation(@PathVariable Long voteId)
    {
        return toAjax(emVoteService.cancelAssociation(voteId));
    }

    /**
     * 开始投票
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:edit')")
    @Log(title = "投票管理", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{voteId}")
    public AjaxResult startVote(@PathVariable Long voteId)
    {
        return toAjax(emVoteService.startVote(voteId));
    }

    /**
     * 结束投票
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:edit')")
    @Log(title = "投票管理", businessType = BusinessType.UPDATE)
    @PostMapping("/end/{voteId}")
    public AjaxResult endVote(@PathVariable Long voteId)
    {
        return toAjax(emVoteService.endVote(voteId));
    }

    /**
     * 用户投票
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:edit')")
    @Log(title = "投票管理", businessType = BusinessType.UPDATE)
    @PostMapping("/userVote")
    public AjaxResult userVote(@RequestParam Long voteId, @RequestParam Long userId, @RequestParam String voteOption)
    {
        return toAjax(emVoteService.userVote(voteId, userId, voteOption));
    }

    /**
     * 补投
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:edit')")
    @Log(title = "投票管理", businessType = BusinessType.UPDATE)
    @PostMapping("/supplement")
    public AjaxResult supplementVote(@RequestParam Long voteId, @RequestParam Long userId, @RequestParam String voteOption)
    {
        return toAjax(emVoteService.supplementVote(voteId, userId, voteOption));
    }

    /**
     * 查询投票明细
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:query')")
    @GetMapping("/details/{voteId}")
    public AjaxResult voteDetails(@PathVariable Long voteId)
    {
        List<Object> details = emVoteService.selectVoteDetails(voteId);
        return AjaxResult.success(details);
    }

    /**
     * 导出投票结果
     */
    @PreAuthorize("@ss.hasPermi('meeting:vote:export')")
    @Log(title = "投票管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportResult/{voteId}")
    public AjaxResult exportVoteResult(@PathVariable Long voteId)
    {
        String filePath = emVoteService.exportVoteResult(voteId);
        return AjaxResult.success("导出成功", filePath);
    }
}
