package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmDevice;
import com.ruoyi.meeting.service.IEmDeviceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/device")
public class EmDeviceController extends BaseController
{
    @Autowired
    private IEmDeviceService emDeviceService;

    /**
     * 查询设备列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmDevice emDevice)
    {
        startPage();
        List<EmDevice> list = emDeviceService.selectEmDeviceList(emDevice);
        return getDataTable(list);
    }

    /**
     * 导出设备列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:export')")
    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmDevice emDevice)
    {
        List<EmDevice> list = emDeviceService.selectEmDeviceList(emDevice);
        ExcelUtil<EmDevice> util = new ExcelUtil<EmDevice>(EmDevice.class);
        util.exportExcel(response, list, "设备数据");
    }

    /**
     * 获取设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:query')")
    @GetMapping(value = "/{deviceId}")
    public AjaxResult getInfo(@PathVariable("deviceId") Long deviceId)
    {
        return AjaxResult.success(emDeviceService.selectEmDeviceByDeviceId(deviceId));
    }

    /**
     * 新增设备
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:add')")
    @Log(title = "设备管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmDevice emDevice)
    {
        return toAjax(emDeviceService.insertEmDevice(emDevice));
    }

    /**
     * 修改设备
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmDevice emDevice)
    {
        return toAjax(emDeviceService.updateEmDevice(emDevice));
    }

    /**
     * 删除设备
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:remove')")
    @Log(title = "设备管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{deviceIds}")
    public AjaxResult remove(@PathVariable Long[] deviceIds)
    {
        return toAjax(emDeviceService.deleteEmDeviceByDeviceIds(deviceIds));
    }

    /**
     * 启用设备
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{deviceId}")
    public AjaxResult enable(@PathVariable Long deviceId)
    {
        return toAjax(emDeviceService.enableDevice(deviceId));
    }

    /**
     * 禁用设备
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PostMapping("/disable/{deviceId}")
    public AjaxResult disable(@PathVariable Long deviceId)
    {
        return toAjax(emDeviceService.disableDevice(deviceId));
    }

    /**
     * 绑定用户
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PostMapping("/bindUsers")
    public AjaxResult bindUsers(@RequestParam Long deviceId, @RequestParam Long[] userIds)
    {
        return toAjax(emDeviceService.bindUsers(deviceId, userIds));
    }

    /**
     * 解绑用户
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PostMapping("/unbindUsers")
    public AjaxResult unbindUsers(@RequestParam Long deviceId, @RequestParam Long[] userIds)
    {
        return toAjax(emDeviceService.unbindUsers(deviceId, userIds));
    }

    /**
     * 批量绑定用户
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batchBindUsers")
    public AjaxResult batchBindUsers(@RequestParam Long[] deviceIds, @RequestParam Long[] userIds)
    {
        return toAjax(emDeviceService.batchBindUsers(deviceIds, userIds));
    }

    /**
     * 批量解绑用户
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUnbindUsers")
    public AjaxResult batchUnbindUsers(@RequestParam Long[] deviceIds, @RequestParam Long[] userIds)
    {
        return toAjax(emDeviceService.batchUnbindUsers(deviceIds, userIds));
    }

    /**
     * 获取设备绑定的用户
     */
    @PreAuthorize("@ss.hasPermi('meeting:device:query')")
    @GetMapping("/users/{deviceId}")
    public AjaxResult getDeviceUsers(@PathVariable Long deviceId)
    {
        List<Long> userIds = emDeviceService.getDeviceUsers(deviceId);
        return AjaxResult.success(userIds);
    }

    /**
     * 检查设备是否可用
     */
    @GetMapping("/checkAvailable")
    public AjaxResult checkDeviceAvailable(@RequestParam String deviceCode, 
                                          @RequestParam(required = false) String macAddress)
    {
        boolean available = emDeviceService.checkDeviceAvailable(deviceCode, macAddress);
        return AjaxResult.success(available);
    }
}
