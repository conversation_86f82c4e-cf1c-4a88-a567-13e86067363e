package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmSubMeeting;
import com.ruoyi.meeting.service.IEmSubMeetingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 子会议Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/submeeting")
public class EmSubMeetingController extends BaseController
{
    @Autowired
    private IEmSubMeetingService emSubMeetingService;

    /**
     * 查询子会议列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmSubMeeting emSubMeeting)
    {
        startPage();
        List<EmSubMeeting> list = emSubMeetingService.selectEmSubMeetingList(emSubMeeting);
        return getDataTable(list);
    }

    /**
     * 根据父会议ID查询子会议列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:list')")
    @GetMapping("/listByParent/{parentMeetingId}")
    public AjaxResult listByParent(@PathVariable Long parentMeetingId)
    {
        List<EmSubMeeting> list = emSubMeetingService.selectEmSubMeetingByParentId(parentMeetingId);
        return AjaxResult.success(list);
    }

    /**
     * 导出子会议列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:export')")
    @Log(title = "子会议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmSubMeeting emSubMeeting)
    {
        List<EmSubMeeting> list = emSubMeetingService.selectEmSubMeetingList(emSubMeeting);
        ExcelUtil<EmSubMeeting> util = new ExcelUtil<EmSubMeeting>(EmSubMeeting.class);
        util.exportExcel(response, list, "子会议数据");
    }

    /**
     * 获取子会议详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:query')")
    @GetMapping(value = "/{subMeetingId}")
    public AjaxResult getInfo(@PathVariable("subMeetingId") Long subMeetingId)
    {
        return AjaxResult.success(emSubMeetingService.selectEmSubMeetingBySubMeetingId(subMeetingId));
    }

    /**
     * 新增子会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:add')")
    @Log(title = "子会议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmSubMeeting emSubMeeting)
    {
        return toAjax(emSubMeetingService.insertEmSubMeeting(emSubMeeting));
    }

    /**
     * 修改子会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:edit')")
    @Log(title = "子会议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmSubMeeting emSubMeeting)
    {
        return toAjax(emSubMeetingService.updateEmSubMeeting(emSubMeeting));
    }

    /**
     * 删除子会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:remove')")
    @Log(title = "子会议", businessType = BusinessType.DELETE)
	@DeleteMapping("/{subMeetingIds}")
    public AjaxResult remove(@PathVariable Long[] subMeetingIds)
    {
        return toAjax(emSubMeetingService.deleteEmSubMeetingBySubMeetingIds(subMeetingIds));
    }

    /**
     * 添加子会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:edit')")
    @Log(title = "子会议", businessType = BusinessType.UPDATE)
    @PostMapping("/participants/add")
    public AjaxResult addParticipants(@RequestParam Long subMeetingId, @RequestParam Long[] userIds)
    {
        return toAjax(emSubMeetingService.addParticipants(subMeetingId, userIds));
    }

    /**
     * 移除子会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:edit')")
    @Log(title = "子会议", businessType = BusinessType.UPDATE)
    @PostMapping("/participants/remove")
    public AjaxResult removeParticipants(@RequestParam Long subMeetingId, @RequestParam Long[] userIds)
    {
        return toAjax(emSubMeetingService.removeParticipants(subMeetingId, userIds));
    }

    /**
     * 获取子会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:submeeting:query')")
    @GetMapping("/participants/{subMeetingId}")
    public AjaxResult getParticipants(@PathVariable Long subMeetingId)
    {
        List<Long> participants = emSubMeetingService.getSubMeetingParticipants(subMeetingId);
        return AjaxResult.success(participants);
    }
}
