package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmNotice;
import com.ruoyi.meeting.service.IEmNoticeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 通知管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/notice")
public class EmNoticeController extends BaseController
{
    @Autowired
    private IEmNoticeService emNoticeService;

    /**
     * 查询通知列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmNotice emNotice)
    {
        startPage();
        List<EmNotice> list = emNoticeService.selectEmNoticeList(emNotice);
        return getDataTable(list);
    }

    /**
     * 导出通知列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:export')")
    @Log(title = "通知管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmNotice emNotice)
    {
        List<EmNotice> list = emNoticeService.selectEmNoticeList(emNotice);
        ExcelUtil<EmNotice> util = new ExcelUtil<EmNotice>(EmNotice.class);
        util.exportExcel(response, list, "通知数据");
    }

    /**
     * 获取通知详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId)
    {
        return AjaxResult.success(emNoticeService.selectEmNoticeByNoticeId(noticeId));
    }

    /**
     * 新增通知
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:add')")
    @Log(title = "通知管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmNotice emNotice)
    {
        return toAjax(emNoticeService.insertEmNotice(emNotice));
    }

    /**
     * 修改通知
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:edit')")
    @Log(title = "通知管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmNotice emNotice)
    {
        return toAjax(emNoticeService.updateEmNotice(emNotice));
    }

    /**
     * 删除通知
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:remove')")
    @Log(title = "通知管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds)
    {
        return toAjax(emNoticeService.deleteEmNoticeByNoticeIds(noticeIds));
    }

    /**
     * 分发通知
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:edit')")
    @Log(title = "通知管理", businessType = BusinessType.UPDATE)
    @PostMapping("/distribute")
    public AjaxResult distributeNotice(@RequestBody EmNotice emNotice)
    {
        return toAjax(emNoticeService.distributeNotice(emNotice));
    }

    /**
     * 查询分发记录
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:list')")
    @GetMapping("/distributionRecord")
    public TableDataInfo distributionRecord(EmNotice emNotice)
    {
        startPage();
        List<EmNotice> list = emNoticeService.selectDistributionRecord(emNotice);
        return getDataTable(list);
    }

    /**
     * 上传附件
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:add')")
    @Log(title = "通知管理", businessType = BusinessType.INSERT)
    @PostMapping("/uploadAttachment")
    public AjaxResult uploadAttachment(@RequestParam("file") MultipartFile file) throws Exception
    {
        String fileName = emNoticeService.uploadAttachment(file);
        return AjaxResult.success("上传成功", fileName);
    }

    /**
     * 下载附件
     */
    @PreAuthorize("@ss.hasPermi('meeting:notice:query')")
    @GetMapping("/downloadAttachment")
    public void downloadAttachment(@RequestParam String filePath, HttpServletResponse response)
    {
        emNoticeService.downloadAttachment(filePath, response);
    }
}
