package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmMeetingType;
import com.ruoyi.meeting.service.IEmMeetingTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 会议类型Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/type")
public class EmMeetingTypeController extends BaseController
{
    @Autowired
    private IEmMeetingTypeService emMeetingTypeService;

    /**
     * 查询会议类型列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:type:list')")
    @GetMapping("/list")
    public AjaxResult list(EmMeetingType emMeetingType)
    {
        List<EmMeetingType> list = emMeetingTypeService.selectEmMeetingTypeList(emMeetingType);
        return AjaxResult.success(emMeetingTypeService.buildMeetingTypeTree(list));
    }

    /**
     * 导出会议类型列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:type:export')")
    @Log(title = "会议类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmMeetingType emMeetingType)
    {
        List<EmMeetingType> list = emMeetingTypeService.selectEmMeetingTypeList(emMeetingType);
        ExcelUtil<EmMeetingType> util = new ExcelUtil<EmMeetingType>(EmMeetingType.class);
        util.exportExcel(response, list, "会议类型数据");
    }

    /**
     * 获取会议类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:type:query')")
    @GetMapping(value = "/{typeId}")
    public AjaxResult getInfo(@PathVariable("typeId") Long typeId)
    {
        return AjaxResult.success(emMeetingTypeService.selectEmMeetingTypeByTypeId(typeId));
    }

    /**
     * 新增会议类型
     */
    @PreAuthorize("@ss.hasPermi('meeting:type:add')")
    @Log(title = "会议类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmMeetingType emMeetingType)
    {
        return toAjax(emMeetingTypeService.insertEmMeetingType(emMeetingType));
    }

    /**
     * 修改会议类型
     */
    @PreAuthorize("@ss.hasPermi('meeting:type:edit')")
    @Log(title = "会议类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmMeetingType emMeetingType)
    {
        return toAjax(emMeetingTypeService.updateEmMeetingType(emMeetingType));
    }

    /**
     * 删除会议类型
     */
    @PreAuthorize("@ss.hasPermi('meeting:type:remove')")
    @Log(title = "会议类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{typeIds}")
    public AjaxResult remove(@PathVariable Long[] typeIds)
    {
        for (Long typeId : typeIds)
        {
            if (emMeetingTypeService.hasChildByTypeId(typeId))
            {
                return AjaxResult.error("存在子类型，不允许删除");
            }
        }
        return toAjax(emMeetingTypeService.deleteEmMeetingTypeByTypeIds(typeIds));
    }

    /**
     * 获取会议类型选择框列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(EmMeetingType emMeetingType)
    {
        List<EmMeetingType> meetingTypes = emMeetingTypeService.selectEmMeetingTypeList(emMeetingType);
        return AjaxResult.success(emMeetingTypeService.buildMeetingTypeTree(meetingTypes));
    }
}
