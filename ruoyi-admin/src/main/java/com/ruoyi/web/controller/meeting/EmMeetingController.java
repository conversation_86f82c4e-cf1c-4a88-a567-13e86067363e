package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmMeeting;
import com.ruoyi.meeting.service.IEmMeetingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 会议管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/meeting")
public class EmMeetingController extends BaseController
{
    @Autowired
    private IEmMeetingService emMeetingService;

    /**
     * 查询会议列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmMeeting emMeeting)
    {
        startPage();
        List<EmMeeting> list = emMeetingService.selectEmMeetingList(emMeeting);
        return getDataTable(list);
    }

    /**
     * 导出会议列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:export')")
    @Log(title = "会议管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmMeeting emMeeting)
    {
        List<EmMeeting> list = emMeetingService.selectEmMeetingList(emMeeting);
        ExcelUtil<EmMeeting> util = new ExcelUtil<EmMeeting>(EmMeeting.class);
        util.exportExcel(response, list, "会议数据");
    }

    /**
     * 获取会议详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:query')")
    @GetMapping(value = "/{meetingId}")
    public AjaxResult getInfo(@PathVariable("meetingId") Long meetingId)
    {
        return AjaxResult.success(emMeetingService.selectEmMeetingByMeetingId(meetingId));
    }

    /**
     * 新增会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:add')")
    @Log(title = "会议管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmMeeting emMeeting)
    {
        return toAjax(emMeetingService.insertEmMeeting(emMeeting));
    }

    /**
     * 修改会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmMeeting emMeeting)
    {
        return toAjax(emMeetingService.updateEmMeeting(emMeeting));
    }

    /**
     * 删除会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:remove')")
    @Log(title = "会议管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{meetingIds}")
    public AjaxResult remove(@PathVariable Long[] meetingIds)
    {
        return toAjax(emMeetingService.deleteEmMeetingByMeetingIds(meetingIds));
    }

    /**
     * 导入会议数据
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:import')")
    @Log(title = "会议管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        String message = emMeetingService.importMeeting(file, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 下载会议导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        String templatePath = emMeetingService.downloadTemplate();
        // 这里实现文件下载逻辑
    }

    /**
     * 更新会议状态
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestParam Long meetingId, @RequestParam String status)
    {
        return toAjax(emMeetingService.updateMeetingStatus(meetingId, status));
    }

    /**
     * 添加会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/participants/add")
    public AjaxResult addParticipants(@RequestParam Long meetingId, @RequestParam Long[] userIds)
    {
        return toAjax(emMeetingService.addParticipants(meetingId, userIds));
    }

    /**
     * 移除会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/participants/remove")
    public AjaxResult removeParticipants(@RequestParam Long meetingId, @RequestParam Long[] userIds)
    {
        return toAjax(emMeetingService.removeParticipants(meetingId, userIds));
    }

    /**
     * 获取会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:query')")
    @GetMapping("/participants/{meetingId}")
    public AjaxResult getParticipants(@PathVariable Long meetingId)
    {
        List<Long> participants = emMeetingService.getMeetingParticipants(meetingId);
        return AjaxResult.success(participants);
    }

    /**
     * 复制上次会议人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/participants/copyLast")
    public AjaxResult copyLastMeetingParticipants(@RequestParam Long meetingId, @RequestParam Long lastMeetingId)
    {
        List<Long> lastParticipants = emMeetingService.getMeetingParticipants(lastMeetingId);
        if (!lastParticipants.isEmpty()) {
            Long[] userIds = lastParticipants.toArray(new Long[0]);
            return toAjax(emMeetingService.addParticipants(meetingId, userIds));
        }
        return AjaxResult.success("没有找到上次会议的参与人员");
    }
}
