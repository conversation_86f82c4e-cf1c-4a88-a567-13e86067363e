package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmAgenda;
import com.ruoyi.meeting.service.IEmAgendaService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 议题管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/agenda")
public class EmAgendaController extends BaseController
{
    @Autowired
    private IEmAgendaService emAgendaService;

    /**
     * 查询议题列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmAgenda emAgenda)
    {
        startPage();
        List<EmAgenda> list = emAgendaService.selectEmAgendaList(emAgenda);
        return getDataTable(list);
    }

    /**
     * 根据子会议ID查询议题列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:list')")
    @GetMapping("/listBySubMeeting/{subMeetingId}")
    public AjaxResult listBySubMeeting(@PathVariable Long subMeetingId)
    {
        List<EmAgenda> list = emAgendaService.selectEmAgendaBySubMeetingId(subMeetingId);
        return AjaxResult.success(list);
    }

    /**
     * 导出议题列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:export')")
    @Log(title = "议题管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmAgenda emAgenda)
    {
        List<EmAgenda> list = emAgendaService.selectEmAgendaList(emAgenda);
        ExcelUtil<EmAgenda> util = new ExcelUtil<EmAgenda>(EmAgenda.class);
        util.exportExcel(response, list, "议题数据");
    }

    /**
     * 获取议题详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:query')")
    @GetMapping(value = "/{agendaId}")
    public AjaxResult getInfo(@PathVariable("agendaId") Long agendaId)
    {
        return AjaxResult.success(emAgendaService.selectEmAgendaByAgendaId(agendaId));
    }

    /**
     * 新增议题
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:add')")
    @Log(title = "议题管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmAgenda emAgenda)
    {
        return toAjax(emAgendaService.insertEmAgenda(emAgenda));
    }

    /**
     * 修改议题
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:edit')")
    @Log(title = "议题管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmAgenda emAgenda)
    {
        return toAjax(emAgendaService.updateEmAgenda(emAgenda));
    }

    /**
     * 删除议题
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:remove')")
    @Log(title = "议题管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{agendaIds}")
    public AjaxResult remove(@PathVariable Long[] agendaIds)
    {
        return toAjax(emAgendaService.deleteEmAgendaByAgendaIds(agendaIds));
    }

    /**
     * 议题排序
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:edit')")
    @Log(title = "议题管理", businessType = BusinessType.UPDATE)
    @PostMapping("/sort")
    public AjaxResult sort(@RequestParam Long[] agendaIds)
    {
        return toAjax(emAgendaService.sortAgenda(agendaIds));
    }

    /**
     * 上移议题
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:edit')")
    @Log(title = "议题管理", businessType = BusinessType.UPDATE)
    @PostMapping("/moveUp/{agendaId}")
    public AjaxResult moveUp(@PathVariable Long agendaId)
    {
        return toAjax(emAgendaService.moveUpAgenda(agendaId));
    }

    /**
     * 下移议题
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:edit')")
    @Log(title = "议题管理", businessType = BusinessType.UPDATE)
    @PostMapping("/moveDown/{agendaId}")
    public AjaxResult moveDown(@PathVariable Long agendaId)
    {
        return toAjax(emAgendaService.moveDownAgenda(agendaId));
    }

    /**
     * 自动创建投票
     */
    @PreAuthorize("@ss.hasPermi('meeting:agenda:edit')")
    @Log(title = "议题管理", businessType = BusinessType.UPDATE)
    @PostMapping("/createVote/{agendaId}")
    public AjaxResult createVote(@PathVariable Long agendaId)
    {
        return toAjax(emAgendaService.autoCreateVote(agendaId));
    }
}
