package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmFile;
import com.ruoyi.meeting.service.IEmFileService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 文件管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/file")
public class EmFileController extends BaseController
{
    @Autowired
    private IEmFileService emFileService;

    /**
     * 查询文件列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmFile emFile)
    {
        startPage();
        List<EmFile> list = emFileService.selectEmFileList(emFile);
        return getDataTable(list);
    }

    /**
     * 根据议题ID查询文件列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:list')")
    @GetMapping("/listByAgenda/{agendaId}")
    public AjaxResult listByAgenda(@PathVariable Long agendaId)
    {
        List<EmFile> list = emFileService.selectEmFileByAgendaId(agendaId);
        return AjaxResult.success(list);
    }

    /**
     * 根据会议ID查询文件列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:list')")
    @GetMapping("/listByMeeting/{meetingId}")
    public AjaxResult listByMeeting(@PathVariable Long meetingId)
    {
        List<EmFile> list = emFileService.selectEmFileByMeetingId(meetingId);
        return AjaxResult.success(list);
    }

    /**
     * 导出文件列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:export')")
    @Log(title = "文件管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmFile emFile)
    {
        List<EmFile> list = emFileService.selectEmFileList(emFile);
        ExcelUtil<EmFile> util = new ExcelUtil<EmFile>(EmFile.class);
        util.exportExcel(response, list, "文件数据");
    }

    /**
     * 获取文件详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:query')")
    @GetMapping(value = "/{fileId}")
    public AjaxResult getInfo(@PathVariable("fileId") Long fileId)
    {
        return AjaxResult.success(emFileService.selectEmFileByFileId(fileId));
    }

    /**
     * 新增文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:add')")
    @Log(title = "文件管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmFile emFile)
    {
        return toAjax(emFileService.insertEmFile(emFile));
    }

    /**
     * 修改文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:edit')")
    @Log(title = "文件管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmFile emFile)
    {
        return toAjax(emFileService.updateEmFile(emFile));
    }

    /**
     * 删除文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:remove')")
    @Log(title = "文件管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{fileIds}")
    public AjaxResult remove(@PathVariable Long[] fileIds)
    {
        return toAjax(emFileService.deleteEmFileByFileIds(fileIds));
    }

    /**
     * 上传文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:add')")
    @Log(title = "文件管理", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file, 
                                @RequestParam(value = "agendaId", required = false) Long agendaId) throws Exception
    {
        String fileName = emFileService.uploadFile(file, agendaId);
        return AjaxResult.success("上传成功", fileName);
    }

    /**
     * 批量上传文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:add')")
    @Log(title = "文件管理", businessType = BusinessType.INSERT)
    @PostMapping("/batchUpload")
    public AjaxResult batchUploadFile(@RequestParam("file") MultipartFile file) throws Exception
    {
        String result = emFileService.batchUploadFile(file);
        return AjaxResult.success(result);
    }

    /**
     * 下载文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:query')")
    @GetMapping("/download/{fileId}")
    public void downloadFile(@PathVariable Long fileId, HttpServletResponse response)
    {
        emFileService.downloadFile(fileId, response);
    }

    /**
     * 在线预览文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:query')")
    @GetMapping("/preview/{fileId}")
    public AjaxResult previewFile(@PathVariable Long fileId, 
                                 @RequestParam(value = "password", required = false) String password)
    {
        String previewUrl = emFileService.previewFile(fileId, password);
        return AjaxResult.success("获取预览地址成功", previewUrl);
    }

    /**
     * 分发文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:edit')")
    @Log(title = "文件管理", businessType = BusinessType.UPDATE)
    @PostMapping("/distribute")
    public AjaxResult distributeFile(@RequestParam Long[] fileIds, @RequestParam Long[] userIds)
    {
        return toAjax(emFileService.distributeFile(fileIds, userIds));
    }

    /**
     * 一键分发文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:edit')")
    @Log(title = "文件管理", businessType = BusinessType.UPDATE)
    @PostMapping("/oneClickDistribute")
    public AjaxResult oneClickDistribute(@RequestParam Long[] fileIds, @RequestParam Long meetingId)
    {
        return toAjax(emFileService.oneClickDistribute(fileIds, meetingId));
    }

    /**
     * 收回文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:edit')")
    @Log(title = "文件管理", businessType = BusinessType.UPDATE)
    @PostMapping("/recall")
    public AjaxResult recallFile(@RequestParam Long[] fileIds, @RequestParam Long[] userIds)
    {
        return toAjax(emFileService.recallFile(fileIds, userIds));
    }

    /**
     * 绑定议题
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:edit')")
    @Log(title = "文件管理", businessType = BusinessType.UPDATE)
    @PostMapping("/bindAgenda")
    public AjaxResult bindAgenda(@RequestParam Long fileId, @RequestParam Long agendaId)
    {
        return toAjax(emFileService.bindAgenda(fileId, agendaId));
    }

    /**
     * 文件排序
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:edit')")
    @Log(title = "文件管理", businessType = BusinessType.UPDATE)
    @PostMapping("/sort")
    public AjaxResult sortFile(@RequestParam Long[] fileIds)
    {
        return toAjax(emFileService.sortFile(fileIds));
    }

    /**
     * 重命名文件
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:edit')")
    @Log(title = "文件管理", businessType = BusinessType.UPDATE)
    @PostMapping("/rename")
    public AjaxResult renameFile(@RequestParam Long fileId, @RequestParam String fileName)
    {
        return toAjax(emFileService.renameFile(fileId, fileName));
    }

    /**
     * 查询分发记录
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:list')")
    @GetMapping("/distributionRecord")
    public TableDataInfo distributionRecord(EmFile emFile)
    {
        startPage();
        List<EmFile> list = emFileService.selectDistributionRecord(emFile);
        return getDataTable(list);
    }

    /**
     * 查询文件笔记
     */
    @PreAuthorize("@ss.hasPermi('meeting:file:list')")
    @GetMapping("/notes/{fileId}")
    public AjaxResult fileNotes(@PathVariable Long fileId)
    {
        List<Object> notes = emFileService.selectFileNotes(fileId);
        return AjaxResult.success(notes);
    }
}
