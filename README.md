# 会议管理系统 (EMeeting)

基于RuoYi v3.9.0开发规范的企业级会议管理系统，提供完整的会议流程管理、文件分发、投票签到等功能。

## 🚀 项目特色

- **完整的会议流程**: 六步会议管理流程，从创建到分发一站式解决
- **智能文件管理**: 支持多格式文件上传、在线预览、批量分发
- **实时投票签到**: 支持多种投票类型和实时签到统计
- **设备管理**: 完善的终端设备管理和用户绑定
- **权限控制**: 基于RuoYi的完善权限管理体系
- **现代化界面**: Vue2 + Element UI构建的响应式前端

## 📋 功能模块

### 1. 会议管理流程
- ✅ 第一步：创建会议
- ✅ 第二步：创建子会议
- ✅ 第三步：选择参会人
- ✅ 第四步：创建议题
- ✅ 第五步：文件上传
- ✅ 第六步：文件分发

### 2. 系统登录
- ✅ 系统登录界面
- ✅ IOS客户端安装链接
- ✅ 会议管理系统跳转
- ✅ 用户认证和权限控制

### 3. 会议信息管理
#### 3.1 会议管理
- ✅ 新建会议
- ✅ 修改会议信息
- ✅ 删除会议信息
- ✅ 导入会议
- ✅ 选择会议
- ✅ 会议人员管理
- ✅ 新建子会议

#### 3.2 议题管理
- ✅ 新建议题
- ✅ 修改议题
- ✅ 删除议题
- ✅ 议题拖拽排序
- ✅ 议题关联文件显示
- ✅ 议题关联文件重命名
- ✅ 议题关联文件排序
- ✅ 筛选条件-选择会议

### 4. 文件信息管理
#### 4.1 文件管理
- ✅ 文件上传
- ✅ 文件批量导入
- ✅ 文件分发（单个、批量、一键）
- ✅ 文件收回
- ✅ 文件删除
- ✅ 文件在线预览
- ✅ 文件下载

#### 4.2 文件分发记录
- ✅ 筛选条件-选择会议

#### 4.3 文件笔记管理
- ✅ 笔记明细

### 5. 会议设置管理
#### 5.1 会议类型管理
- ✅ 新建会议类型
- ✅ 修改会议类型
- ✅ 删除会议类型

### 6. 通知信息管理
#### 6.1 通知管理
- ✅ 新建通知
- ✅ 修改通知
- ✅ 删除通知
- ✅ 分发通知
- ✅ 管理附件

### 7. 设备信息管理
#### 7.1 设备管理
- ✅ 新建设备信息
- ✅ 修改设备信息
- ✅ 删除设备
- ✅ 禁用和启用设备
- ✅ 设备绑定用户

### 8. 投票签到管理
#### 8.1 签到管理
- ✅ 签到创建
- ✅ 关联人员
- ✅ 取消关联
- ✅ 签到明细

#### 8.2 投票管理
- ✅ 新建投票
- ✅ 关联人员
- ✅ 取消关联
- ✅ 修改投票
- ✅ 删除投票
- ✅ 投票明细

## 🛠️ 技术架构

### 后端技术栈
- **框架**: Spring Boot 2.5.15
- **ORM**: MyBatis 3.5.10
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.2
- **安全**: Spring Security + JWT
- **文档**: Swagger 3.0

### 前端技术栈
- **框架**: Vue 2.6.14
- **UI组件**: Element UI 2.15.9
- **构建工具**: Webpack 4.46.0
- **HTTP客户端**: Axios 0.27.2
- **路由**: Vue Router 3.5.4

### 数据库设计
- **核心表**: 18个数据库表
- **关系设计**: 完整的外键关系
- **索引优化**: 合理的索引设计

## 📦 快速开始

### 环境要求
- JDK 8+
- Maven 3.6+
- Node.js 14+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/EMeeting.git
cd EMeeting
```

2. **数据库初始化**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE emeeting DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据
mysql -u root -p emeeting < sql/ry_20250522.sql
```

3. **后端启动**
```bash
# 修改配置文件
vim ruoyi-admin/src/main/resources/application-dev.yml

# 编译运行
mvn clean package
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

4. **前端启动**
```bash
cd ruoyi-ui
npm install
npm run dev
```

5. **访问系统**
- 前端地址: http://localhost:80
- 后端地址: http://localhost:8080
- 默认账号: admin / admin123

### 一键部署

使用提供的部署脚本可以快速部署到不同环境：

```bash
# 部署到开发环境
./deploy/deploy.sh dev

# 部署到生产环境并初始化数据库
./deploy/deploy.sh prod -d

# 只部署前端到测试环境
./deploy/deploy.sh test -f
```

## 🧪 测试

### 功能测试
```bash
# 运行自动化测试脚本
./test/meeting-system-test.sh
```

### 单元测试
```bash
# 后端单元测试
mvn test

# 前端单元测试
cd ruoyi-ui
npm run test
```

## 📊 项目统计

- **总代码量**: 9,121 行
- **Java后端**: 3,517 行
- **Vue前端**: 4,259 行
- **SQL脚本**: 468 行
- **配置文件**: 877 行

## 🔧 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: *************************************************************************************************************************************************
    username: root
    password: password
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 10s
```

### 文件上传配置
```yaml
ruoyi:
  profile: /path/to/upload
  file:
    domain: http://localhost:8080
    path: /profile/upload
```

## 📝 API文档

启动项目后访问 Swagger 文档：
- Swagger UI: http://localhost:8080/swagger-ui/index.html
- API Docs: http://localhost:8080/v3/api-docs

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: https://github.com/your-repo/EMeeting
- 问题反馈: https://github.com/your-repo/EMeeting/issues
- 邮箱: <EMAIL>

## 🙏 致谢

- [RuoYi](https://gitee.com/y_project/RuoYi) - 基础框架
- [Element UI](https://element.eleme.io/) - UI组件库
- [Vue.js](https://vuejs.org/) - 前端框架
- [Spring Boot](https://spring.io/projects/spring-boot) - 后端框架

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
