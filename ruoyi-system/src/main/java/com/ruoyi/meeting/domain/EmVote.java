package com.ruoyi.meeting.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 投票信息对象 em_vote
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class EmVote extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 投票ID */
    private Long voteId;

    /** 投票标题 */
    @Excel(name = "投票标题")
    private String voteTitle;

    /** 投票内容 */
    @Excel(name = "投票内容")
    private String voteContent;

    /** 子会议ID */
    @Excel(name = "子会议ID")
    private Long subMeetingId;

    /** 子会议名称 */
    @Excel(name = "子会议名称")
    private String subMeetingName;

    /** 关联议题ID */
    @Excel(name = "关联议题ID")
    private Long agendaId;

    /** 议题标题 */
    @Excel(name = "议题标题")
    private String agendaTitle;

    /** 投票类型（0赞成/反对 1多选 2单选） */
    @Excel(name = "投票类型", readConverterExp = "0=赞成/反对,1=多选,2=单选")
    private String voteType;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 总票数 */
    @Excel(name = "总票数")
    private Integer totalCount;

    /** 赞成票数 */
    @Excel(name = "赞成票数")
    private Integer agreeCount;

    /** 反对票数 */
    @Excel(name = "反对票数")
    private Integer disagreeCount;

    /** 弃权票数 */
    @Excel(name = "弃权票数")
    private Integer abstainCount;

    /** 状态（0未开始 1进行中 2已结束） */
    @Excel(name = "状态", readConverterExp = "0=未开始,1=进行中,2=已结束")
    private String status;

    /** 参与用户ID数组 */
    private Long[] userIds;

    /** 参与用户名数组 */
    private String[] userNames;

    public void setVoteId(Long voteId) 
    {
        this.voteId = voteId;
    }

    public Long getVoteId() 
    {
        return voteId;
    }
    public void setVoteTitle(String voteTitle) 
    {
        this.voteTitle = voteTitle;
    }

    public String getVoteTitle() 
    {
        return voteTitle;
    }
    public void setVoteContent(String voteContent) 
    {
        this.voteContent = voteContent;
    }

    public String getVoteContent() 
    {
        return voteContent;
    }
    public void setSubMeetingId(Long subMeetingId) 
    {
        this.subMeetingId = subMeetingId;
    }

    public Long getSubMeetingId() 
    {
        return subMeetingId;
    }
    public void setSubMeetingName(String subMeetingName) 
    {
        this.subMeetingName = subMeetingName;
    }

    public String getSubMeetingName() 
    {
        return subMeetingName;
    }
    public void setAgendaId(Long agendaId) 
    {
        this.agendaId = agendaId;
    }

    public Long getAgendaId() 
    {
        return agendaId;
    }
    public void setAgendaTitle(String agendaTitle) 
    {
        this.agendaTitle = agendaTitle;
    }

    public String getAgendaTitle() 
    {
        return agendaTitle;
    }
    public void setVoteType(String voteType) 
    {
        this.voteType = voteType;
    }

    public String getVoteType() 
    {
        return voteType;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setTotalCount(Integer totalCount) 
    {
        this.totalCount = totalCount;
    }

    public Integer getTotalCount() 
    {
        return totalCount;
    }
    public void setAgreeCount(Integer agreeCount) 
    {
        this.agreeCount = agreeCount;
    }

    public Integer getAgreeCount() 
    {
        return agreeCount;
    }
    public void setDisagreeCount(Integer disagreeCount) 
    {
        this.disagreeCount = disagreeCount;
    }

    public Integer getDisagreeCount() 
    {
        return disagreeCount;
    }
    public void setAbstainCount(Integer abstainCount) 
    {
        this.abstainCount = abstainCount;
    }

    public Integer getAbstainCount() 
    {
        return abstainCount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public Long[] getUserIds() 
    {
        return userIds;
    }

    public void setUserIds(Long[] userIds) 
    {
        this.userIds = userIds;
    }

    public String[] getUserNames() 
    {
        return userNames;
    }

    public void setUserNames(String[] userNames) 
    {
        this.userNames = userNames;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("voteId", getVoteId())
            .append("voteTitle", getVoteTitle())
            .append("voteContent", getVoteContent())
            .append("subMeetingId", getSubMeetingId())
            .append("agendaId", getAgendaId())
            .append("voteType", getVoteType())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("totalCount", getTotalCount())
            .append("agreeCount", getAgreeCount())
            .append("disagreeCount", getDisagreeCount())
            .append("abstainCount", getAbstainCount())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
