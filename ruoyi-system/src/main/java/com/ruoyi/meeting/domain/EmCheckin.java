package com.ruoyi.meeting.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 签到信息对象 em_checkin
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class EmCheckin extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 签到ID */
    private Long checkinId;

    /** 子会议ID */
    @Excel(name = "子会议ID")
    private Long subMeetingId;

    /** 子会议名称 */
    @Excel(name = "子会议名称")
    private String subMeetingName;

    /** 签到标题 */
    @Excel(name = "签到标题")
    private String checkinTitle;

    /** 签到描述 */
    @Excel(name = "签到描述")
    private String checkinDesc;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 签到人数 */
    @Excel(name = "签到人数")
    private Integer checkinCount;

    /** 总人数 */
    @Excel(name = "总人数")
    private Integer totalCount;

    /** 状态（0未开始 1进行中 2已结束） */
    @Excel(name = "状态", readConverterExp = "0=未开始,1=进行中,2=已结束")
    private String status;

    /** 参与用户ID数组 */
    private Long[] userIds;

    /** 参与用户名数组 */
    private String[] userNames;

    public void setCheckinId(Long checkinId) 
    {
        this.checkinId = checkinId;
    }

    public Long getCheckinId() 
    {
        return checkinId;
    }
    public void setSubMeetingId(Long subMeetingId) 
    {
        this.subMeetingId = subMeetingId;
    }

    public Long getSubMeetingId() 
    {
        return subMeetingId;
    }
    public void setSubMeetingName(String subMeetingName) 
    {
        this.subMeetingName = subMeetingName;
    }

    public String getSubMeetingName() 
    {
        return subMeetingName;
    }
    public void setCheckinTitle(String checkinTitle) 
    {
        this.checkinTitle = checkinTitle;
    }

    public String getCheckinTitle() 
    {
        return checkinTitle;
    }
    public void setCheckinDesc(String checkinDesc) 
    {
        this.checkinDesc = checkinDesc;
    }

    public String getCheckinDesc() 
    {
        return checkinDesc;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setCheckinCount(Integer checkinCount) 
    {
        this.checkinCount = checkinCount;
    }

    public Integer getCheckinCount() 
    {
        return checkinCount;
    }
    public void setTotalCount(Integer totalCount) 
    {
        this.totalCount = totalCount;
    }

    public Integer getTotalCount() 
    {
        return totalCount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public Long[] getUserIds() 
    {
        return userIds;
    }

    public void setUserIds(Long[] userIds) 
    {
        this.userIds = userIds;
    }

    public String[] getUserNames() 
    {
        return userNames;
    }

    public void setUserNames(String[] userNames) 
    {
        this.userNames = userNames;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("checkinId", getCheckinId())
            .append("subMeetingId", getSubMeetingId())
            .append("subMeetingName", getSubMeetingName())
            .append("checkinTitle", getCheckinTitle())
            .append("checkinDesc", getCheckinDesc())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("checkinCount", getCheckinCount())
            .append("totalCount", getTotalCount())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
