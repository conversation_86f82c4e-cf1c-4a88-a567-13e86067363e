package com.ruoyi.meeting.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 子会议对象 em_sub_meeting
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class EmSubMeeting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 子会议ID */
    private Long subMeetingId;

    /** 父会议ID */
    @Excel(name = "父会议ID")
    private Long parentMeetingId;

    /** 父会议名称 */
    @Excel(name = "父会议名称")
    private String parentMeetingName;

    /** 子会议名称 */
    @Excel(name = "子会议名称")
    private String subMeetingName;

    /** 子会议描述 */
    @Excel(name = "子会议描述")
    private String subMeetingDesc;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 会议地点 */
    @Excel(name = "会议地点")
    private String location;

    /** 组织者 */
    @Excel(name = "组织者")
    private String organizer;

    /** 状态（0未开始 1进行中 2已结束 3已取消） */
    @Excel(name = "状态", readConverterExp = "0=未开始,1=进行中,2=已结束,3=已取消")
    private String status;

    public void setSubMeetingId(Long subMeetingId) 
    {
        this.subMeetingId = subMeetingId;
    }

    public Long getSubMeetingId() 
    {
        return subMeetingId;
    }
    public void setParentMeetingId(Long parentMeetingId) 
    {
        this.parentMeetingId = parentMeetingId;
    }

    public Long getParentMeetingId() 
    {
        return parentMeetingId;
    }
    public void setParentMeetingName(String parentMeetingName) 
    {
        this.parentMeetingName = parentMeetingName;
    }

    public String getParentMeetingName() 
    {
        return parentMeetingName;
    }
    public void setSubMeetingName(String subMeetingName) 
    {
        this.subMeetingName = subMeetingName;
    }

    public String getSubMeetingName() 
    {
        return subMeetingName;
    }
    public void setSubMeetingDesc(String subMeetingDesc) 
    {
        this.subMeetingDesc = subMeetingDesc;
    }

    public String getSubMeetingDesc() 
    {
        return subMeetingDesc;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    public void setOrganizer(String organizer) 
    {
        this.organizer = organizer;
    }

    public String getOrganizer() 
    {
        return organizer;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("subMeetingId", getSubMeetingId())
            .append("parentMeetingId", getParentMeetingId())
            .append("subMeetingName", getSubMeetingName())
            .append("subMeetingDesc", getSubMeetingDesc())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("location", getLocation())
            .append("organizer", getOrganizer())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
