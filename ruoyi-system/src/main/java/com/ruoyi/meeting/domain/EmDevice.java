package com.ruoyi.meeting.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 设备信息对象 em_device
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class EmDevice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设备ID */
    private Long deviceId;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String deviceCode;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private String deviceType;

    /** 设备型号 */
    @Excel(name = "设备型号")
    private String deviceModel;

    /** MAC地址 */
    @Excel(name = "MAC地址")
    private String macAddress;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ipAddress;

    /** 绑定用户ID */
    @Excel(name = "绑定用户ID")
    private Long bindUserId;

    /** 绑定用户名 */
    @Excel(name = "绑定用户名")
    private String bindUserName;

    /** 绑定数量 */
    @Excel(name = "绑定数量")
    private Integer bindCount;

    /** 状态（0正常 1禁用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=禁用")
    private String status;

    /** 绑定用户ID数组 */
    private Long[] userIds;

    /** 绑定用户名数组 */
    private String[] userNames;

    public void setDeviceId(Long deviceId) 
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() 
    {
        return deviceId;
    }
    public void setDeviceName(String deviceName) 
    {
        this.deviceName = deviceName;
    }

    public String getDeviceName() 
    {
        return deviceName;
    }
    public void setDeviceCode(String deviceCode) 
    {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() 
    {
        return deviceCode;
    }
    public void setDeviceType(String deviceType) 
    {
        this.deviceType = deviceType;
    }

    public String getDeviceType() 
    {
        return deviceType;
    }
    public void setDeviceModel(String deviceModel) 
    {
        this.deviceModel = deviceModel;
    }

    public String getDeviceModel() 
    {
        return deviceModel;
    }
    public void setMacAddress(String macAddress) 
    {
        this.macAddress = macAddress;
    }

    public String getMacAddress() 
    {
        return macAddress;
    }
    public void setIpAddress(String ipAddress) 
    {
        this.ipAddress = ipAddress;
    }

    public String getIpAddress() 
    {
        return ipAddress;
    }
    public void setBindUserId(Long bindUserId) 
    {
        this.bindUserId = bindUserId;
    }

    public Long getBindUserId() 
    {
        return bindUserId;
    }
    public void setBindUserName(String bindUserName) 
    {
        this.bindUserName = bindUserName;
    }

    public String getBindUserName() 
    {
        return bindUserName;
    }
    public void setBindCount(Integer bindCount) 
    {
        this.bindCount = bindCount;
    }

    public Integer getBindCount() 
    {
        return bindCount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public Long[] getUserIds() 
    {
        return userIds;
    }

    public void setUserIds(Long[] userIds) 
    {
        this.userIds = userIds;
    }

    public String[] getUserNames() 
    {
        return userNames;
    }

    public void setUserNames(String[] userNames) 
    {
        this.userNames = userNames;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("deviceId", getDeviceId())
            .append("deviceName", getDeviceName())
            .append("deviceCode", getDeviceCode())
            .append("deviceType", getDeviceType())
            .append("deviceModel", getDeviceModel())
            .append("macAddress", getMacAddress())
            .append("ipAddress", getIpAddress())
            .append("bindUserId", getBindUserId())
            .append("bindUserName", getBindUserName())
            .append("bindCount", getBindCount())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
