package com.ruoyi.meeting.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文件信息对象 em_file
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class EmFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文件ID */
    private Long fileId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 原始文件名 */
    @Excel(name = "原始文件名")
    private String originalName;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    /** 文件大小 */
    @Excel(name = "文件大小")
    private Long fileSize;

    /** 文件类型 */
    @Excel(name = "文件类型")
    private String fileType;

    /** 文件扩展名 */
    @Excel(name = "文件扩展名")
    private String fileExt;

    /** 关联议题ID */
    @Excel(name = "关联议题ID")
    private Long agendaId;

    /** 议题标题 */
    @Excel(name = "议题标题")
    private String agendaTitle;

    /** 关联会议ID */
    @Excel(name = "关联会议ID")
    private Long meetingId;

    /** 会议名称 */
    @Excel(name = "会议名称")
    private String meetingName;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer orderNum;

    /** 是否有密码（0否 1是） */
    @Excel(name = "是否有密码", readConverterExp = "0=否,1=是")
    private String isPassword;

    /** 文件密码 */
    private String password;

    /** 状态（0正常 1禁用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=禁用")
    private String status;

    public void setFileId(Long fileId) 
    {
        this.fileId = fileId;
    }

    public Long getFileId() 
    {
        return fileId;
    }
    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }
    public void setOriginalName(String originalName) 
    {
        this.originalName = originalName;
    }

    public String getOriginalName() 
    {
        return originalName;
    }
    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }
    public void setFileSize(Long fileSize) 
    {
        this.fileSize = fileSize;
    }

    public Long getFileSize() 
    {
        return fileSize;
    }
    public void setFileType(String fileType) 
    {
        this.fileType = fileType;
    }

    public String getFileType() 
    {
        return fileType;
    }
    public void setFileExt(String fileExt) 
    {
        this.fileExt = fileExt;
    }

    public String getFileExt() 
    {
        return fileExt;
    }
    public void setAgendaId(Long agendaId) 
    {
        this.agendaId = agendaId;
    }

    public Long getAgendaId() 
    {
        return agendaId;
    }
    public void setAgendaTitle(String agendaTitle) 
    {
        this.agendaTitle = agendaTitle;
    }

    public String getAgendaTitle() 
    {
        return agendaTitle;
    }
    public void setMeetingId(Long meetingId) 
    {
        this.meetingId = meetingId;
    }

    public Long getMeetingId() 
    {
        return meetingId;
    }
    public void setMeetingName(String meetingName) 
    {
        this.meetingName = meetingName;
    }

    public String getMeetingName() 
    {
        return meetingName;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setIsPassword(String isPassword) 
    {
        this.isPassword = isPassword;
    }

    public String getIsPassword() 
    {
        return isPassword;
    }
    public void setPassword(String password) 
    {
        this.password = password;
    }

    public String getPassword() 
    {
        return password;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("fileId", getFileId())
            .append("fileName", getFileName())
            .append("originalName", getOriginalName())
            .append("filePath", getFilePath())
            .append("fileSize", getFileSize())
            .append("fileType", getFileType())
            .append("fileExt", getFileExt())
            .append("agendaId", getAgendaId())
            .append("meetingId", getMeetingId())
            .append("orderNum", getOrderNum())
            .append("isPassword", getIsPassword())
            .append("password", getPassword())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
