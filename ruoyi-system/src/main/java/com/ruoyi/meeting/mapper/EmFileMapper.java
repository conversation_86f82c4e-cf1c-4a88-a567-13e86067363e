package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmFile;

/**
 * 文件管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmFileMapper 
{
    /**
     * 查询文件
     * 
     * @param fileId 文件主键
     * @return 文件
     */
    public EmFile selectEmFileByFileId(Long fileId);

    /**
     * 查询文件列表
     * 
     * @param emFile 文件
     * @return 文件集合
     */
    public List<EmFile> selectEmFileList(EmFile emFile);

    /**
     * 根据议题ID查询文件列表
     * 
     * @param agendaId 议题ID
     * @return 文件集合
     */
    public List<EmFile> selectEmFileByAgendaId(Long agendaId);

    /**
     * 根据会议ID查询文件列表
     * 
     * @param meetingId 会议ID
     * @return 文件集合
     */
    public List<EmFile> selectEmFileByMeetingId(Long meetingId);

    /**
     * 新增文件
     * 
     * @param emFile 文件
     * @return 结果
     */
    public int insertEmFile(EmFile emFile);

    /**
     * 修改文件
     * 
     * @param emFile 文件
     * @return 结果
     */
    public int updateEmFile(EmFile emFile);

    /**
     * 删除文件
     * 
     * @param fileId 文件主键
     * @return 结果
     */
    public int deleteEmFileByFileId(Long fileId);

    /**
     * 批量删除文件
     * 
     * @param fileIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmFileByFileIds(Long[] fileIds);

    /**
     * 更新文件排序
     * 
     * @param fileId 文件ID
     * @param orderNum 排序号
     * @return 结果
     */
    public int updateFileOrder(Long fileId, Integer orderNum);

    /**
     * 绑定议题
     * 
     * @param fileId 文件ID
     * @param agendaId 议题ID
     * @return 结果
     */
    public int bindAgenda(Long fileId, Long agendaId);
}
