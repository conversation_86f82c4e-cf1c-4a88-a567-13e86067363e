package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmMeeting;

/**
 * 会议管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmMeetingMapper 
{
    /**
     * 查询会议
     * 
     * @param meetingId 会议主键
     * @return 会议
     */
    public EmMeeting selectEmMeetingByMeetingId(Long meetingId);

    /**
     * 查询会议列表
     * 
     * @param emMeeting 会议
     * @return 会议集合
     */
    public List<EmMeeting> selectEmMeetingList(EmMeeting emMeeting);

    /**
     * 新增会议
     * 
     * @param emMeeting 会议
     * @return 结果
     */
    public int insertEmMeeting(EmMeeting emMeeting);

    /**
     * 修改会议
     * 
     * @param emMeeting 会议
     * @return 结果
     */
    public int updateEmMeeting(EmMeeting emMeeting);

    /**
     * 删除会议
     * 
     * @param meetingId 会议主键
     * @return 结果
     */
    public int deleteEmMeetingByMeetingId(Long meetingId);

    /**
     * 批量删除会议
     * 
     * @param meetingIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmMeetingByMeetingIds(Long[] meetingIds);

    /**
     * 更新会议状态
     * 
     * @param meetingId 会议ID
     * @param status 状态
     * @return 结果
     */
    public int updateMeetingStatus(Long meetingId, String status);
}
