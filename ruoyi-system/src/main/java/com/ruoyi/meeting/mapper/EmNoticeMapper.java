package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmNotice;

/**
 * 通知管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmNoticeMapper 
{
    /**
     * 查询通知
     * 
     * @param noticeId 通知主键
     * @return 通知
     */
    public EmNotice selectEmNoticeByNoticeId(Long noticeId);

    /**
     * 查询通知列表
     * 
     * @param emNotice 通知
     * @return 通知集合
     */
    public List<EmNotice> selectEmNoticeList(EmNotice emNotice);

    /**
     * 新增通知
     * 
     * @param emNotice 通知
     * @return 结果
     */
    public int insertEmNotice(EmNotice emNotice);

    /**
     * 修改通知
     * 
     * @param emNotice 通知
     * @return 结果
     */
    public int updateEmNotice(EmNotice emNotice);

    /**
     * 删除通知
     * 
     * @param noticeId 通知主键
     * @return 结果
     */
    public int deleteEmNoticeByNoticeId(Long noticeId);

    /**
     * 批量删除通知
     * 
     * @param noticeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmNoticeByNoticeIds(Long[] noticeIds);
}
