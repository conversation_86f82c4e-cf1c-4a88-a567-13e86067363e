package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmVote;

/**
 * 投票管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmVoteMapper
{
    /**
     * 查询投票
     *
     * @param voteId 投票主键
     * @return 投票
     */
    public EmVote selectEmVoteByVoteId(Long voteId);

    /**
     * 查询投票列表
     *
     * @param emVote 投票
     * @return 投票集合
     */
    public List<EmVote> selectEmVoteList(EmVote emVote);

    /**
     * 新增投票
     *
     * @param emVote 投票
     * @return 结果
     */
    public int insertEmVote(EmVote emVote);

    /**
     * 修改投票
     *
     * @param emVote 投票
     * @return 结果
     */
    public int updateEmVote(EmVote emVote);

    /**
     * 删除投票
     *
     * @param voteId 投票主键
     * @return 结果
     */
    public int deleteEmVoteByVoteId(Long voteId);

    /**
     * 批量删除投票
     *
     * @param voteIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmVoteByVoteIds(Long[] voteIds);

    /**
     * 更新投票状态
     *
     * @param voteId 投票ID
     * @param status 状态
     * @return 结果
     */
    public int updateVoteStatus(Long voteId, String status);
}
