package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmVote;

public interface EmVoteMapper 
{
    public EmVote selectEmVoteByVoteId(Long voteId);
    public List<EmVote> selectEmVoteList(EmVote emVote);
    public int insertEmVote(EmVote emVote);
    public int updateEmVote(EmVote emVote);
    public int deleteEmVoteByVoteId(Long voteId);
    public int deleteEmVoteByVoteIds(Long[] voteIds);
    public int updateVoteStatus(Long voteId, String status);
}
