package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmCheckin;

/**
 * 签到管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmCheckinMapper 
{
    /**
     * 查询签到
     * 
     * @param checkinId 签到主键
     * @return 签到
     */
    public EmCheckin selectEmCheckinByCheckinId(Long checkinId);

    /**
     * 查询签到列表
     * 
     * @param emCheckin 签到
     * @return 签到集合
     */
    public List<EmCheckin> selectEmCheckinList(EmCheckin emCheckin);

    /**
     * 根据子会议ID查询签到
     * 
     * @param subMeetingId 子会议ID
     * @return 签到
     */
    public EmCheckin selectEmCheckinBySubMeetingId(Long subMeetingId);

    /**
     * 新增签到
     * 
     * @param emCheckin 签到
     * @return 结果
     */
    public int insertEmCheckin(EmCheckin emCheckin);

    /**
     * 修改签到
     * 
     * @param emCheckin 签到
     * @return 结果
     */
    public int updateEmCheckin(EmCheckin emCheckin);

    /**
     * 删除签到
     * 
     * @param checkinId 签到主键
     * @return 结果
     */
    public int deleteEmCheckinByCheckinId(Long checkinId);

    /**
     * 批量删除签到
     * 
     * @param checkinIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmCheckinByCheckinIds(Long[] checkinIds);

    /**
     * 更新签到状态
     * 
     * @param checkinId 签到ID
     * @param status 状态
     * @return 结果
     */
    public int updateCheckinStatus(Long checkinId, String status);
}
