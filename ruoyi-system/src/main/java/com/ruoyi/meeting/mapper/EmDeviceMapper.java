package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmDevice;

public interface EmDeviceMapper 
{
    public EmDevice selectEmDeviceByDeviceId(Long deviceId);
    public List<EmDevice> selectEmDeviceList(EmDevice emDevice);
    public int insertEmDevice(EmDevice emDevice);
    public int updateEmDevice(EmDevice emDevice);
    public int deleteEmDeviceByDeviceId(Long deviceId);
    public int deleteEmDeviceByDeviceIds(Long[] deviceIds);
    public int updateDeviceStatus(Long deviceId, String status);
}
