package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmMeetingType;

/**
 * 会议类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmMeetingTypeMapper 
{
    /**
     * 查询会议类型
     * 
     * @param typeId 会议类型主键
     * @return 会议类型
     */
    public EmMeetingType selectEmMeetingTypeByTypeId(Long typeId);

    /**
     * 查询会议类型列表
     * 
     * @param emMeetingType 会议类型
     * @return 会议类型集合
     */
    public List<EmMeetingType> selectEmMeetingTypeList(EmMeetingType emMeetingType);

    /**
     * 新增会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    public int insertEmMeetingType(EmMeetingType emMeetingType);

    /**
     * 修改会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    public int updateEmMeetingType(EmMeetingType emMeetingType);

    /**
     * 删除会议类型
     * 
     * @param typeId 会议类型主键
     * @return 结果
     */
    public int deleteEmMeetingTypeByTypeId(Long typeId);

    /**
     * 批量删除会议类型
     * 
     * @param typeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmMeetingTypeByTypeIds(Long[] typeIds);

    /**
     * 查询子类型数量
     * 
     * @param parentId 父类型ID
     * @return 结果
     */
    public int selectChildrenCount(Long parentId);
}
