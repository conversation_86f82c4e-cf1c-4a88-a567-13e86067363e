package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmAgenda;

/**
 * 议题管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmAgendaMapper 
{
    /**
     * 查询议题
     * 
     * @param agendaId 议题主键
     * @return 议题
     */
    public EmAgenda selectEmAgendaByAgendaId(Long agendaId);

    /**
     * 查询议题列表
     * 
     * @param emAgenda 议题
     * @return 议题集合
     */
    public List<EmAgenda> selectEmAgendaList(EmAgenda emAgenda);

    /**
     * 根据子会议ID查询议题列表
     * 
     * @param subMeetingId 子会议ID
     * @return 议题集合
     */
    public List<EmAgenda> selectEmAgendaBySubMeetingId(Long subMeetingId);

    /**
     * 新增议题
     * 
     * @param emAgenda 议题
     * @return 结果
     */
    public int insertEmAgenda(EmAgenda emAgenda);

    /**
     * 修改议题
     * 
     * @param emAgenda 议题
     * @return 结果
     */
    public int updateEmAgenda(EmAgenda emAgenda);

    /**
     * 删除议题
     * 
     * @param agendaId 议题主键
     * @return 结果
     */
    public int deleteEmAgendaByAgendaId(Long agendaId);

    /**
     * 批量删除议题
     * 
     * @param agendaIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmAgendaByAgendaIds(Long[] agendaIds);

    /**
     * 根据子会议ID删除议题
     * 
     * @param subMeetingId 子会议ID
     * @return 结果
     */
    public int deleteEmAgendaBySubMeetingId(Long subMeetingId);

    /**
     * 更新议题排序
     * 
     * @param agendaId 议题ID
     * @param orderNum 排序号
     * @return 结果
     */
    public int updateAgendaOrder(Long agendaId, Integer orderNum);
}
