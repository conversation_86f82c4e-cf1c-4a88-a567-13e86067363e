package com.ruoyi.meeting.service;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.meeting.domain.EmNotice;

/**
 * 通知管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmNoticeService
{
    /**
     * 查询通知
     *
     * @param noticeId 通知主键
     * @return 通知
     */
    public EmNotice selectEmNoticeByNoticeId(Long noticeId);

    /**
     * 查询通知列表
     *
     * @param emNotice 通知
     * @return 通知集合
     */
    public List<EmNotice> selectEmNoticeList(EmNotice emNotice);

    /**
     * 新增通知
     *
     * @param emNotice 通知
     * @return 结果
     */
    public int insertEmNotice(EmNotice emNotice);

    /**
     * 修改通知
     *
     * @param emNotice 通知
     * @return 结果
     */
    public int updateEmNotice(EmNotice emNotice);

    /**
     * 批量删除通知
     *
     * @param noticeIds 需要删除的通知主键集合
     * @return 结果
     */
    public int deleteEmNoticeByNoticeIds(Long[] noticeIds);

    /**
     * 删除通知信息
     *
     * @param noticeId 通知主键
     * @return 结果
     */
    public int deleteEmNoticeByNoticeId(Long noticeId);

    /**
     * 分发通知
     *
     * @param emNotice 通知信息
     * @return 结果
     */
    public int distributeNotice(EmNotice emNotice);

    /**
     * 查询分发记录
     *
     * @param emNotice 查询条件
     * @return 分发记录
     */
    public List<EmNotice> selectDistributionRecord(EmNotice emNotice);

    /**
     * 上传附件
     *
     * @param file 附件文件
     * @return 文件名
     */
    public String uploadAttachment(MultipartFile file) throws Exception;

    /**
     * 下载附件
     *
     * @param filePath 文件路径
     * @param response HTTP响应
     */
    public void downloadAttachment(String filePath, HttpServletResponse response);
}
