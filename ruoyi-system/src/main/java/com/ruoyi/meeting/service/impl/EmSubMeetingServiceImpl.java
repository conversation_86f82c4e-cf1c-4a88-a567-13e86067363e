package com.ruoyi.meeting.service.impl;

import java.util.List;
import java.util.ArrayList;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.meeting.mapper.EmSubMeetingMapper;
import com.ruoyi.meeting.mapper.EmAgendaMapper;
import com.ruoyi.meeting.mapper.EmCheckinMapper;
import com.ruoyi.meeting.domain.EmSubMeeting;
import com.ruoyi.meeting.domain.EmCheckin;
import com.ruoyi.meeting.service.IEmSubMeetingService;

/**
 * 子会议Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class EmSubMeetingServiceImpl implements IEmSubMeetingService 
{
    @Autowired
    private EmSubMeetingMapper emSubMeetingMapper;

    @Autowired
    private EmAgendaMapper emAgendaMapper;

    @Autowired
    private EmCheckinMapper emCheckinMapper;

    /**
     * 查询子会议
     * 
     * @param subMeetingId 子会议主键
     * @return 子会议
     */
    @Override
    public EmSubMeeting selectEmSubMeetingBySubMeetingId(Long subMeetingId)
    {
        return emSubMeetingMapper.selectEmSubMeetingBySubMeetingId(subMeetingId);
    }

    /**
     * 查询子会议列表
     * 
     * @param emSubMeeting 子会议
     * @return 子会议
     */
    @Override
    public List<EmSubMeeting> selectEmSubMeetingList(EmSubMeeting emSubMeeting)
    {
        return emSubMeetingMapper.selectEmSubMeetingList(emSubMeeting);
    }

    /**
     * 根据父会议ID查询子会议列表
     * 
     * @param parentMeetingId 父会议ID
     * @return 子会议集合
     */
    @Override
    public List<EmSubMeeting> selectEmSubMeetingByParentId(Long parentMeetingId)
    {
        return emSubMeetingMapper.selectEmSubMeetingByParentId(parentMeetingId);
    }

    /**
     * 新增子会议
     * 
     * @param emSubMeeting 子会议
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEmSubMeeting(EmSubMeeting emSubMeeting)
    {
        emSubMeeting.setCreateTime(DateUtils.getNowDate());
        int result = emSubMeetingMapper.insertEmSubMeeting(emSubMeeting);
        
        // 自动创建签到信息
        if (result > 0) {
            EmCheckin checkin = new EmCheckin();
            checkin.setSubMeetingId(emSubMeeting.getSubMeetingId());
            checkin.setSubMeetingName(emSubMeeting.getSubMeetingName());
            checkin.setCheckinTitle(emSubMeeting.getSubMeetingName() + "签到");
            checkin.setCheckinDesc("自动创建的签到");
            checkin.setStartTime(emSubMeeting.getStartTime());
            checkin.setEndTime(emSubMeeting.getEndTime());
            checkin.setStatus("0");
            checkin.setCreateTime(DateUtils.getNowDate());
            emCheckinMapper.insertEmCheckin(checkin);
        }
        
        return result;
    }

    /**
     * 修改子会议
     * 
     * @param emSubMeeting 子会议
     * @return 结果
     */
    @Override
    public int updateEmSubMeeting(EmSubMeeting emSubMeeting)
    {
        emSubMeeting.setUpdateTime(DateUtils.getNowDate());
        return emSubMeetingMapper.updateEmSubMeeting(emSubMeeting);
    }

    /**
     * 批量删除子会议
     * 
     * @param subMeetingIds 需要删除的子会议主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmSubMeetingBySubMeetingIds(Long[] subMeetingIds)
    {
        // 删除子会议前先删除相关的议题
        for (Long subMeetingId : subMeetingIds) {
            emAgendaMapper.deleteEmAgendaBySubMeetingId(subMeetingId);
        }
        return emSubMeetingMapper.deleteEmSubMeetingBySubMeetingIds(subMeetingIds);
    }

    /**
     * 删除子会议信息
     * 
     * @param subMeetingId 子会议主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmSubMeetingBySubMeetingId(Long subMeetingId)
    {
        // 删除子会议前先删除相关的议题
        emAgendaMapper.deleteEmAgendaBySubMeetingId(subMeetingId);
        return emSubMeetingMapper.deleteEmSubMeetingBySubMeetingId(subMeetingId);
    }

    /**
     * 添加子会议参与人员
     * 
     * @param subMeetingId 子会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int addParticipants(Long subMeetingId, Long[] userIds)
    {
        // 实现添加参与人员的逻辑
        return userIds.length;
    }

    /**
     * 移除子会议参与人员
     * 
     * @param subMeetingId 子会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int removeParticipants(Long subMeetingId, Long[] userIds)
    {
        // 实现移除参与人员的逻辑
        return userIds.length;
    }

    /**
     * 获取子会议参与人员
     * 
     * @param subMeetingId 子会议ID
     * @return 参与人员列表
     */
    @Override
    public List<Long> getSubMeetingParticipants(Long subMeetingId)
    {
        // 实现获取参与人员的逻辑
        return new ArrayList<>();
    }
}
