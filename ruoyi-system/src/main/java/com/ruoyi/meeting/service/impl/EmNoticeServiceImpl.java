package com.ruoyi.meeting.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.meeting.mapper.EmNoticeMapper;
import com.ruoyi.meeting.domain.EmNotice;
import com.ruoyi.meeting.service.IEmNoticeService;

/**
 * 通知管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class EmNoticeServiceImpl implements IEmNoticeService 
{
    @Autowired
    private EmNoticeMapper emNoticeMapper;

    /**
     * 查询通知
     * 
     * @param noticeId 通知主键
     * @return 通知
     */
    @Override
    public EmNotice selectEmNoticeByNoticeId(Long noticeId)
    {
        return emNoticeMapper.selectEmNoticeByNoticeId(noticeId);
    }

    /**
     * 查询通知列表
     * 
     * @param emNotice 通知
     * @return 通知
     */
    @Override
    public List<EmNotice> selectEmNoticeList(EmNotice emNotice)
    {
        return emNoticeMapper.selectEmNoticeList(emNotice);
    }

    /**
     * 新增通知
     * 
     * @param emNotice 通知
     * @return 结果
     */
    @Override
    public int insertEmNotice(EmNotice emNotice)
    {
        emNotice.setCreateTime(DateUtils.getNowDate());
        return emNoticeMapper.insertEmNotice(emNotice);
    }

    /**
     * 修改通知
     * 
     * @param emNotice 通知
     * @return 结果
     */
    @Override
    public int updateEmNotice(EmNotice emNotice)
    {
        emNotice.setUpdateTime(DateUtils.getNowDate());
        return emNoticeMapper.updateEmNotice(emNotice);
    }

    /**
     * 批量删除通知
     * 
     * @param noticeIds 需要删除的通知主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmNoticeByNoticeIds(Long[] noticeIds)
    {
        // 删除通知前先删除附件
        for (Long noticeId : noticeIds) {
            EmNotice notice = emNoticeMapper.selectEmNoticeByNoticeId(noticeId);
            if (notice != null && StringUtils.isNotEmpty(notice.getAttachmentPath())) {
                try {
                    FileUtils.deleteFile(notice.getAttachmentPath());
                } catch (Exception e) {
                    // 记录日志，但不影响数据库删除
                }
            }
        }
        return emNoticeMapper.deleteEmNoticeByNoticeIds(noticeIds);
    }

    /**
     * 删除通知信息
     * 
     * @param noticeId 通知主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmNoticeByNoticeId(Long noticeId)
    {
        // 删除通知前先删除附件
        EmNotice notice = emNoticeMapper.selectEmNoticeByNoticeId(noticeId);
        if (notice != null && StringUtils.isNotEmpty(notice.getAttachmentPath())) {
            try {
                FileUtils.deleteFile(notice.getAttachmentPath());
            } catch (Exception e) {
                // 记录日志，但不影响数据库删除
            }
        }
        return emNoticeMapper.deleteEmNoticeByNoticeId(noticeId);
    }

    /**
     * 分发通知
     * 
     * @param emNotice 通知信息
     * @return 结果
     */
    @Override
    @Transactional
    public int distributeNotice(EmNotice emNotice)
    {
        // 实现通知分发逻辑
        // 1. 验证通知信息
        // 2. 创建分发记录
        // 3. 发送通知
        
        Long[] userIds = emNotice.getUserIds();
        if (userIds == null || userIds.length == 0) {
            throw new ServiceException("请选择分发对象");
        }
        
        // 这里实现具体的分发逻辑
        // 可以通过消息队列、邮件、短信等方式发送通知
        
        return userIds.length;
    }

    /**
     * 查询分发记录
     * 
     * @param emNotice 查询条件
     * @return 分发记录
     */
    @Override
    public List<EmNotice> selectDistributionRecord(EmNotice emNotice)
    {
        // 实现分发记录查询逻辑
        // 这里需要关联通知分发记录表
        return new ArrayList<>();
    }

    /**
     * 上传附件
     * 
     * @param file 附件文件
     * @return 文件名
     */
    @Override
    public String uploadAttachment(MultipartFile file) throws Exception
    {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        
        // 上传文件
        String fileName = FileUploadUtils.upload(RuoYiConfig.getUploadPath(), file);
        
        return fileName;
    }

    /**
     * 下载附件
     * 
     * @param filePath 文件路径
     * @param response HTTP响应
     */
    @Override
    public void downloadAttachment(String filePath, HttpServletResponse response)
    {
        if (StringUtils.isEmpty(filePath)) {
            throw new ServiceException("文件路径不能为空");
        }
        
        try {
            FileUtils.writeBytes(filePath, response.getOutputStream());
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        } catch (IOException e) {
            throw new ServiceException("文件下载失败");
        }
    }
}
