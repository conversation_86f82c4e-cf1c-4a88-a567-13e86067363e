package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmDevice;

/**
 * 设备管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmDeviceService 
{
    /**
     * 查询设备
     * 
     * @param deviceId 设备主键
     * @return 设备
     */
    public EmDevice selectEmDeviceByDeviceId(Long deviceId);

    /**
     * 查询设备列表
     * 
     * @param emDevice 设备
     * @return 设备集合
     */
    public List<EmDevice> selectEmDeviceList(EmDevice emDevice);

    /**
     * 新增设备
     * 
     * @param emDevice 设备
     * @return 结果
     */
    public int insertEmDevice(EmDevice emDevice);

    /**
     * 修改设备
     * 
     * @param emDevice 设备
     * @return 结果
     */
    public int updateEmDevice(EmDevice emDevice);

    /**
     * 批量删除设备
     * 
     * @param deviceIds 需要删除的设备主键集合
     * @return 结果
     */
    public int deleteEmDeviceByDeviceIds(Long[] deviceIds);

    /**
     * 删除设备信息
     * 
     * @param deviceId 设备主键
     * @return 结果
     */
    public int deleteEmDeviceByDeviceId(Long deviceId);

    /**
     * 绑定用户
     * 
     * @param emDevice 设备信息
     * @return 结果
     */
    public int bindUser(EmDevice emDevice);

    /**
     * 解绑用户
     * 
     * @param deviceId 设备ID
     * @return 结果
     */
    public int unbindUser(Long deviceId);

    /**
     * 批量绑定用户
     * 
     * @param emDevice 设备信息
     * @return 结果
     */
    public int batchBindUser(EmDevice emDevice);

    /**
     * 更新设备状态
     * 
     * @param deviceId 设备ID
     * @param status 状态
     * @return 结果
     */
    public int updateDeviceStatus(Long deviceId, String status);
}
