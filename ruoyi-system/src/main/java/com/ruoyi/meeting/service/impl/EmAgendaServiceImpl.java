package com.ruoyi.meeting.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.meeting.mapper.EmAgendaMapper;
import com.ruoyi.meeting.mapper.EmVoteMapper;
import com.ruoyi.meeting.domain.EmAgenda;
import com.ruoyi.meeting.domain.EmVote;
import com.ruoyi.meeting.service.IEmAgendaService;

/**
 * 议题管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class EmAgendaServiceImpl implements IEmAgendaService 
{
    @Autowired
    private EmAgendaMapper emAgendaMapper;

    @Autowired
    private EmVoteMapper emVoteMapper;

    /**
     * 查询议题
     * 
     * @param agendaId 议题主键
     * @return 议题
     */
    @Override
    public EmAgenda selectEmAgendaByAgendaId(Long agendaId)
    {
        return emAgendaMapper.selectEmAgendaByAgendaId(agendaId);
    }

    /**
     * 查询议题列表
     * 
     * @param emAgenda 议题
     * @return 议题
     */
    @Override
    public List<EmAgenda> selectEmAgendaList(EmAgenda emAgenda)
    {
        return emAgendaMapper.selectEmAgendaList(emAgenda);
    }

    /**
     * 根据子会议ID查询议题列表
     * 
     * @param subMeetingId 子会议ID
     * @return 议题集合
     */
    @Override
    public List<EmAgenda> selectEmAgendaBySubMeetingId(Long subMeetingId)
    {
        return emAgendaMapper.selectEmAgendaBySubMeetingId(subMeetingId);
    }

    /**
     * 新增议题
     * 
     * @param emAgenda 议题
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEmAgenda(EmAgenda emAgenda)
    {
        emAgenda.setCreateTime(DateUtils.getNowDate());
        int result = emAgendaMapper.insertEmAgenda(emAgenda);
        
        // 自动创建投票
        if (result > 0) {
            autoCreateVote(emAgenda.getAgendaId());
        }
        
        return result;
    }

    /**
     * 修改议题
     * 
     * @param emAgenda 议题
     * @return 结果
     */
    @Override
    public int updateEmAgenda(EmAgenda emAgenda)
    {
        emAgenda.setUpdateTime(DateUtils.getNowDate());
        return emAgendaMapper.updateEmAgenda(emAgenda);
    }

    /**
     * 批量删除议题
     * 
     * @param agendaIds 需要删除的议题主键
     * @return 结果
     */
    @Override
    public int deleteEmAgendaByAgendaIds(Long[] agendaIds)
    {
        return emAgendaMapper.deleteEmAgendaByAgendaIds(agendaIds);
    }

    /**
     * 删除议题信息
     * 
     * @param agendaId 议题主键
     * @return 结果
     */
    @Override
    public int deleteEmAgendaByAgendaId(Long agendaId)
    {
        return emAgendaMapper.deleteEmAgendaByAgendaId(agendaId);
    }

    /**
     * 议题排序
     * 
     * @param agendaIds 议题ID数组（按排序后的顺序）
     * @return 结果
     */
    @Override
    @Transactional
    public int sortAgenda(Long[] agendaIds)
    {
        int result = 0;
        for (int i = 0; i < agendaIds.length; i++) {
            result += emAgendaMapper.updateAgendaOrder(agendaIds[i], i + 1);
        }
        return result;
    }

    /**
     * 上移议题
     * 
     * @param agendaId 议题ID
     * @return 结果
     */
    @Override
    @Transactional
    public int moveUpAgenda(Long agendaId)
    {
        EmAgenda agenda = emAgendaMapper.selectEmAgendaByAgendaId(agendaId);
        if (agenda == null || agenda.getOrderNum() <= 1) {
            return 0;
        }
        
        // 查找上一个议题
        EmAgenda searchAgenda = new EmAgenda();
        searchAgenda.setSubMeetingId(agenda.getSubMeetingId());
        List<EmAgenda> agendaList = emAgendaMapper.selectEmAgendaList(searchAgenda);
        
        EmAgenda prevAgenda = null;
        for (EmAgenda item : agendaList) {
            if (item.getOrderNum().equals(agenda.getOrderNum() - 1)) {
                prevAgenda = item;
                break;
            }
        }
        
        if (prevAgenda != null) {
            // 交换排序号
            emAgendaMapper.updateAgendaOrder(agendaId, prevAgenda.getOrderNum());
            emAgendaMapper.updateAgendaOrder(prevAgenda.getAgendaId(), agenda.getOrderNum());
            return 1;
        }
        
        return 0;
    }

    /**
     * 下移议题
     * 
     * @param agendaId 议题ID
     * @return 结果
     */
    @Override
    @Transactional
    public int moveDownAgenda(Long agendaId)
    {
        EmAgenda agenda = emAgendaMapper.selectEmAgendaByAgendaId(agendaId);
        if (agenda == null) {
            return 0;
        }
        
        // 查找下一个议题
        EmAgenda searchAgenda = new EmAgenda();
        searchAgenda.setSubMeetingId(agenda.getSubMeetingId());
        List<EmAgenda> agendaList = emAgendaMapper.selectEmAgendaList(searchAgenda);
        
        EmAgenda nextAgenda = null;
        for (EmAgenda item : agendaList) {
            if (item.getOrderNum().equals(agenda.getOrderNum() + 1)) {
                nextAgenda = item;
                break;
            }
        }
        
        if (nextAgenda != null) {
            // 交换排序号
            emAgendaMapper.updateAgendaOrder(agendaId, nextAgenda.getOrderNum());
            emAgendaMapper.updateAgendaOrder(nextAgenda.getAgendaId(), agenda.getOrderNum());
            return 1;
        }
        
        return 0;
    }

    /**
     * 自动创建投票
     * 
     * @param agendaId 议题ID
     * @return 结果
     */
    @Override
    @Transactional
    public int autoCreateVote(Long agendaId)
    {
        EmAgenda agenda = emAgendaMapper.selectEmAgendaByAgendaId(agendaId);
        if (agenda == null) {
            return 0;
        }
        
        EmVote vote = new EmVote();
        vote.setVoteTitle(agenda.getAgendaTitle());
        vote.setVoteContent(agenda.getAgendaContent());
        vote.setSubMeetingId(agenda.getSubMeetingId());
        vote.setAgendaId(agendaId);
        vote.setVoteType("0"); // 赞成/反对
        vote.setStatus("0");
        vote.setCreateTime(DateUtils.getNowDate());
        
        return emVoteMapper.insertEmVote(vote);
    }
}
