package com.ruoyi.meeting.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.meeting.mapper.EmMeetingTypeMapper;
import com.ruoyi.meeting.domain.EmMeetingType;
import com.ruoyi.meeting.service.IEmMeetingTypeService;

/**
 * 会议类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class EmMeetingTypeServiceImpl implements IEmMeetingTypeService 
{
    @Autowired
    private EmMeetingTypeMapper emMeetingTypeMapper;

    /**
     * 查询会议类型
     * 
     * @param typeId 会议类型主键
     * @return 会议类型
     */
    @Override
    public EmMeetingType selectEmMeetingTypeByTypeId(Long typeId)
    {
        return emMeetingTypeMapper.selectEmMeetingTypeByTypeId(typeId);
    }

    /**
     * 查询会议类型列表
     * 
     * @param emMeetingType 会议类型
     * @return 会议类型
     */
    @Override
    public List<EmMeetingType> selectEmMeetingTypeList(EmMeetingType emMeetingType)
    {
        return emMeetingTypeMapper.selectEmMeetingTypeList(emMeetingType);
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param meetingTypes 会议类型列表
     * @return 树结构列表
     */
    @Override
    public List<EmMeetingType> buildMeetingTypeTree(List<EmMeetingType> meetingTypes)
    {
        List<EmMeetingType> returnList = new ArrayList<EmMeetingType>();
        List<Long> tempList = new ArrayList<Long>();
        for (EmMeetingType meetingType : meetingTypes)
        {
            tempList.add(meetingType.getTypeId());
        }
        for (Iterator<EmMeetingType> iterator = meetingTypes.iterator(); iterator.hasNext();)
        {
            EmMeetingType meetingType = (EmMeetingType) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(meetingType.getParentId()))
            {
                recursionFn(meetingTypes, meetingType);
                returnList.add(meetingType);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = meetingTypes;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<EmMeetingType> list, EmMeetingType t)
    {
        // 得到子节点列表
        List<EmMeetingType> childList = getChildList(list, t);
        t.setChildren(childList);
        for (EmMeetingType tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<EmMeetingType> getChildList(List<EmMeetingType> list, EmMeetingType t)
    {
        List<EmMeetingType> tlist = new ArrayList<EmMeetingType>();
        Iterator<EmMeetingType> it = list.iterator();
        while (it.hasNext())
        {
            EmMeetingType n = (EmMeetingType) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getTypeId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<EmMeetingType> list, EmMeetingType t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * 新增会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    @Override
    public int insertEmMeetingType(EmMeetingType emMeetingType)
    {
        emMeetingType.setCreateTime(DateUtils.getNowDate());
        return emMeetingTypeMapper.insertEmMeetingType(emMeetingType);
    }

    /**
     * 修改会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    @Override
    public int updateEmMeetingType(EmMeetingType emMeetingType)
    {
        emMeetingType.setUpdateTime(DateUtils.getNowDate());
        return emMeetingTypeMapper.updateEmMeetingType(emMeetingType);
    }

    /**
     * 批量删除会议类型
     * 
     * @param typeIds 需要删除的会议类型主键
     * @return 结果
     */
    @Override
    public int deleteEmMeetingTypeByTypeIds(Long[] typeIds)
    {
        return emMeetingTypeMapper.deleteEmMeetingTypeByTypeIds(typeIds);
    }

    /**
     * 删除会议类型信息
     * 
     * @param typeId 会议类型主键
     * @return 结果
     */
    @Override
    public int deleteEmMeetingTypeByTypeId(Long typeId)
    {
        return emMeetingTypeMapper.deleteEmMeetingTypeByTypeId(typeId);
    }

    /**
     * 是否存在子节点
     * 
     * @param typeId 会议类型ID
     * @return 结果
     */
    @Override
    public boolean hasChildByTypeId(Long typeId)
    {
        int result = emMeetingTypeMapper.selectChildrenCount(typeId);
        return result > 0 ? true : false;
    }
}
