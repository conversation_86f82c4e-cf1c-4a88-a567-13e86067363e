package com.ruoyi.meeting.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.meeting.mapper.EmDeviceMapper;
import com.ruoyi.meeting.domain.EmDevice;
import com.ruoyi.meeting.service.IEmDeviceService;

@Service
public class EmDeviceServiceImpl implements IEmDeviceService 
{
    @Autowired
    private EmDeviceMapper emDeviceMapper;

    @Override
    public EmDevice selectEmDeviceByDeviceId(Long deviceId)
    {
        return emDeviceMapper.selectEmDeviceByDeviceId(deviceId);
    }

    @Override
    public List<EmDevice> selectEmDeviceList(EmDevice emDevice)
    {
        return emDeviceMapper.selectEmDeviceList(emDevice);
    }

    @Override
    public int insertEmDevice(EmDevice emDevice)
    {
        emDevice.setCreateTime(DateUtils.getNowDate());
        return emDeviceMapper.insertEmDevice(emDevice);
    }

    @Override
    public int updateEmDevice(EmDevice emDevice)
    {
        emDevice.setUpdateTime(DateUtils.getNowDate());
        return emDeviceMapper.updateEmDevice(emDevice);
    }

    @Override
    public int deleteEmDeviceByDeviceIds(Long[] deviceIds)
    {
        return emDeviceMapper.deleteEmDeviceByDeviceIds(deviceIds);
    }

    @Override
    public int deleteEmDeviceByDeviceId(Long deviceId)
    {
        return emDeviceMapper.deleteEmDeviceByDeviceId(deviceId);
    }

    @Override
    public int bindUser(EmDevice emDevice)
    {
        return 1;
    }

    @Override
    public int unbindUser(Long deviceId)
    {
        return 1;
    }

    @Override
    public int batchBindUser(EmDevice emDevice)
    {
        return 1;
    }

    @Override
    public int updateDeviceStatus(Long deviceId, String status)
    {
        return emDeviceMapper.updateDeviceStatus(deviceId, status);
    }
}
