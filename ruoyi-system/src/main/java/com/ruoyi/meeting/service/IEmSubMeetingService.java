package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmSubMeeting;

/**
 * 子会议Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmSubMeetingService 
{
    /**
     * 查询子会议
     * 
     * @param subMeetingId 子会议主键
     * @return 子会议
     */
    public EmSubMeeting selectEmSubMeetingBySubMeetingId(Long subMeetingId);

    /**
     * 查询子会议列表
     * 
     * @param emSubMeeting 子会议
     * @return 子会议集合
     */
    public List<EmSubMeeting> selectEmSubMeetingList(EmSubMeeting emSubMeeting);

    /**
     * 根据父会议ID查询子会议列表
     * 
     * @param parentMeetingId 父会议ID
     * @return 子会议集合
     */
    public List<EmSubMeeting> selectEmSubMeetingByParentId(Long parentMeetingId);

    /**
     * 新增子会议
     * 
     * @param emSubMeeting 子会议
     * @return 结果
     */
    public int insertEmSubMeeting(EmSubMeeting emSubMeeting);

    /**
     * 修改子会议
     * 
     * @param emSubMeeting 子会议
     * @return 结果
     */
    public int updateEmSubMeeting(EmSubMeeting emSubMeeting);

    /**
     * 批量删除子会议
     * 
     * @param subMeetingIds 需要删除的子会议主键集合
     * @return 结果
     */
    public int deleteEmSubMeetingBySubMeetingIds(Long[] subMeetingIds);

    /**
     * 删除子会议信息
     * 
     * @param subMeetingId 子会议主键
     * @return 结果
     */
    public int deleteEmSubMeetingBySubMeetingId(Long subMeetingId);

    /**
     * 添加子会议参与人员
     * 
     * @param subMeetingId 子会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int addParticipants(Long subMeetingId, Long[] userIds);

    /**
     * 移除子会议参与人员
     * 
     * @param subMeetingId 子会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int removeParticipants(Long subMeetingId, Long[] userIds);

    /**
     * 获取子会议参与人员
     * 
     * @param subMeetingId 子会议ID
     * @return 参与人员列表
     */
    public List<Long> getSubMeetingParticipants(Long subMeetingId);
}
