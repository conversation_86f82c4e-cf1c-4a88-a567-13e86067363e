package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmCheckin;

/**
 * 签到管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmCheckinService 
{
    /**
     * 查询签到
     * 
     * @param checkinId 签到主键
     * @return 签到
     */
    public EmCheckin selectEmCheckinByCheckinId(Long checkinId);

    /**
     * 查询签到列表
     * 
     * @param emCheckin 签到
     * @return 签到集合
     */
    public List<EmCheckin> selectEmCheckinList(EmCheckin emCheckin);

    /**
     * 根据子会议ID查询签到
     * 
     * @param subMeetingId 子会议ID
     * @return 签到
     */
    public EmCheckin selectEmCheckinBySubMeetingId(Long subMeetingId);

    /**
     * 新增签到
     * 
     * @param emCheckin 签到
     * @return 结果
     */
    public int insertEmCheckin(EmCheckin emCheckin);

    /**
     * 修改签到
     * 
     * @param emCheckin 签到
     * @return 结果
     */
    public int updateEmCheckin(EmCheckin emCheckin);

    /**
     * 批量删除签到
     * 
     * @param checkinIds 需要删除的签到主键集合
     * @return 结果
     */
    public int deleteEmCheckinByCheckinIds(Long[] checkinIds);

    /**
     * 删除签到信息
     * 
     * @param checkinId 签到主键
     * @return 结果
     */
    public int deleteEmCheckinByCheckinId(Long checkinId);

    /**
     * 关联人员
     * 
     * @param checkinId 签到ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int associateUsers(Long checkinId, Long[] userIds);

    /**
     * 取消关联
     * 
     * @param checkinId 签到ID
     * @return 结果
     */
    public int cancelAssociation(Long checkinId);

    /**
     * 开始签到
     * 
     * @param checkinId 签到ID
     * @return 结果
     */
    public int startCheckin(Long checkinId);

    /**
     * 结束签到
     * 
     * @param checkinId 签到ID
     * @return 结果
     */
    public int endCheckin(Long checkinId);

    /**
     * 用户签到
     * 
     * @param checkinId 签到ID
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     * @param ipAddress IP地址
     * @return 结果
     */
    public int userCheckin(Long checkinId, Long userId, String deviceInfo, String ipAddress);

    /**
     * 补签
     * 
     * @param checkinId 签到ID
     * @param userId 用户ID
     * @return 结果
     */
    public int supplementCheckin(Long checkinId, Long userId);

    /**
     * 查询签到明细
     * 
     * @param checkinId 签到ID
     * @return 签到记录列表
     */
    public List<Object> selectCheckinDetails(Long checkinId);

    /**
     * 导出签到结果
     * 
     * @param checkinId 签到ID
     * @return 文件路径
     */
    public String exportCheckinResult(Long checkinId);

    /**
     * 自动创建签到
     * 
     * @param subMeetingId 子会议ID
     * @return 结果
     */
    public int autoCreateCheckin(Long subMeetingId);
}
