package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmAgenda;

/**
 * 议题管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmAgendaService 
{
    /**
     * 查询议题
     * 
     * @param agendaId 议题主键
     * @return 议题
     */
    public EmAgenda selectEmAgendaByAgendaId(Long agendaId);

    /**
     * 查询议题列表
     * 
     * @param emAgenda 议题
     * @return 议题集合
     */
    public List<EmAgenda> selectEmAgendaList(EmAgenda emAgenda);

    /**
     * 根据子会议ID查询议题列表
     * 
     * @param subMeetingId 子会议ID
     * @return 议题集合
     */
    public List<EmAgenda> selectEmAgendaBySubMeetingId(Long subMeetingId);

    /**
     * 新增议题
     * 
     * @param emAgenda 议题
     * @return 结果
     */
    public int insertEmAgenda(EmAgenda emAgenda);

    /**
     * 修改议题
     * 
     * @param emAgenda 议题
     * @return 结果
     */
    public int updateEmAgenda(EmAgenda emAgenda);

    /**
     * 批量删除议题
     * 
     * @param agendaIds 需要删除的议题主键集合
     * @return 结果
     */
    public int deleteEmAgendaByAgendaIds(Long[] agendaIds);

    /**
     * 删除议题信息
     * 
     * @param agendaId 议题主键
     * @return 结果
     */
    public int deleteEmAgendaByAgendaId(Long agendaId);

    /**
     * 议题排序
     * 
     * @param agendaIds 议题ID数组（按排序后的顺序）
     * @return 结果
     */
    public int sortAgenda(Long[] agendaIds);

    /**
     * 上移议题
     * 
     * @param agendaId 议题ID
     * @return 结果
     */
    public int moveUpAgenda(Long agendaId);

    /**
     * 下移议题
     * 
     * @param agendaId 议题ID
     * @return 结果
     */
    public int moveDownAgenda(Long agendaId);

    /**
     * 自动创建投票
     * 
     * @param agendaId 议题ID
     * @return 结果
     */
    public int autoCreateVote(Long agendaId);
}
