package com.ruoyi.meeting.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.meeting.mapper.EmVoteMapper;
import com.ruoyi.meeting.domain.EmVote;
import com.ruoyi.meeting.service.IEmVoteService;

@Service
public class EmVoteServiceImpl implements IEmVoteService 
{
    @Autowired
    private EmVoteMapper emVoteMapper;

    @Override
    public EmVote selectEmVoteByVoteId(Long voteId)
    {
        return emVoteMapper.selectEmVoteByVoteId(voteId);
    }

    @Override
    public List<EmVote> selectEmVoteList(EmVote emVote)
    {
        return emVoteMapper.selectEmVoteList(emVote);
    }

    @Override
    public int insertEmVote(EmVote emVote)
    {
        emVote.setCreateTime(DateUtils.getNowDate());
        return emVoteMapper.insertEmVote(emVote);
    }

    @Override
    public int updateEmVote(EmVote emVote)
    {
        emVote.setUpdateTime(DateUtils.getNowDate());
        return emVoteMapper.updateEmVote(emVote);
    }

    @Override
    public int deleteEmVoteByVoteIds(Long[] voteIds)
    {
        return emVoteMapper.deleteEmVoteByVoteIds(voteIds);
    }

    @Override
    public int deleteEmVoteByVoteId(Long voteId)
    {
        return emVoteMapper.deleteEmVoteByVoteId(voteId);
    }

    @Override
    public int updateVoteStatus(Long voteId, String status)
    {
        return emVoteMapper.updateVoteStatus(voteId, status);
    }

    @Override
    public int autoCreateVote(Long subMeetingId)
    {
        return 1;
    }

    @Override
    public int relateParticipants(Long voteId, Long[] userIds)
    {
        return 1;
    }

    @Override
    public List<EmVote> selectVoteRecords(EmVote emVote)
    {
        return emVoteMapper.selectEmVoteList(emVote);
    }

    @Override
    public int supplementVote(EmVote emVote)
    {
        return 1;
    }
}
