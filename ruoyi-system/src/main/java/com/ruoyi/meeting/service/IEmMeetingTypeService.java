package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmMeetingType;

/**
 * 会议类型Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmMeetingTypeService 
{
    /**
     * 查询会议类型
     * 
     * @param typeId 会议类型主键
     * @return 会议类型
     */
    public EmMeetingType selectEmMeetingTypeByTypeId(Long typeId);

    /**
     * 查询会议类型列表
     * 
     * @param emMeetingType 会议类型
     * @return 会议类型集合
     */
    public List<EmMeetingType> selectEmMeetingTypeList(EmMeetingType emMeetingType);

    /**
     * 构建前端所需要树结构
     * 
     * @param meetingTypes 会议类型列表
     * @return 树结构列表
     */
    public List<EmMeetingType> buildMeetingTypeTree(List<EmMeetingType> meetingTypes);

    /**
     * 新增会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    public int insertEmMeetingType(EmMeetingType emMeetingType);

    /**
     * 修改会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    public int updateEmMeetingType(EmMeetingType emMeetingType);

    /**
     * 批量删除会议类型
     * 
     * @param typeIds 需要删除的会议类型主键集合
     * @return 结果
     */
    public int deleteEmMeetingTypeByTypeIds(Long[] typeIds);

    /**
     * 删除会议类型信息
     * 
     * @param typeId 会议类型主键
     * @return 结果
     */
    public int deleteEmMeetingTypeByTypeId(Long typeId);

    /**
     * 是否存在子节点
     * 
     * @param typeId 会议类型ID
     * @return 结果
     */
    public boolean hasChildByTypeId(Long typeId);
}
