<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmCheckinMapper">
    
    <resultMap type="EmCheckin" id="EmCheckinResult">
        <result property="checkinId"    column="checkin_id"    />
        <result property="subMeetingId"    column="sub_meeting_id"    />
        <result property="subMeetingName"    column="sub_meeting_name"    />
        <result property="checkinTitle"    column="checkin_title"    />
        <result property="checkinDesc"    column="checkin_desc"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="checkinCount"    column="checkin_count"    />
        <result property="totalCount"    column="total_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmCheckinVo">
        select checkin_id, sub_meeting_id, sub_meeting_name, checkin_title, checkin_desc, start_time, end_time, status, checkin_count, total_count, create_by, create_time, update_by, update_time, remark from em_checkin
    </sql>

    <select id="selectEmCheckinList" parameterType="EmCheckin" resultMap="EmCheckinResult">
        <include refid="selectEmCheckinVo"/>
        <where>  
            <if test="subMeetingId != null "> and sub_meeting_id = #{subMeetingId}</if>
            <if test="checkinTitle != null  and checkinTitle != ''"> and checkin_title like concat('%', #{checkinTitle}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEmCheckinByCheckinId" parameterType="Long" resultMap="EmCheckinResult">
        <include refid="selectEmCheckinVo"/>
        where checkin_id = #{checkinId}
    </select>
        
    <insert id="insertEmCheckin" parameterType="EmCheckin" useGeneratedKeys="true" keyProperty="checkinId">
        insert into em_checkin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subMeetingId != null">sub_meeting_id,</if>
            <if test="checkinTitle != null and checkinTitle != ''">checkin_title,</if>
            <if test="checkinDesc != null">checkin_desc,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subMeetingId != null">#{subMeetingId},</if>
            <if test="checkinTitle != null and checkinTitle != ''">#{checkinTitle},</if>
            <if test="checkinDesc != null">#{checkinDesc},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmCheckin" parameterType="EmCheckin">
        update em_checkin
        <trim prefix="SET" suffixOverrides=",">
            <if test="subMeetingId != null">sub_meeting_id = #{subMeetingId},</if>
            <if test="checkinTitle != null and checkinTitle != ''">checkin_title = #{checkinTitle},</if>
            <if test="checkinDesc != null">checkin_desc = #{checkinDesc},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where checkin_id = #{checkinId}
    </update>

    <delete id="deleteEmCheckinByCheckinId" parameterType="Long">
        delete from em_checkin where checkin_id = #{checkinId}
    </delete>

    <delete id="deleteEmCheckinByCheckinIds" parameterType="String">
        delete from em_checkin where checkin_id in 
        <foreach item="checkinId" collection="array" open="(" separator="," close=")">
            #{checkinId}
        </foreach>
    </delete>

</mapper>
