<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmMeetingTypeMapper">
    
    <resultMap type="EmMeetingType" id="EmMeetingTypeResult">
        <result property="typeId"    column="type_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="typeName"    column="type_name"    />
        <result property="typeDesc"    column="type_desc"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmMeetingTypeVo">
        select type_id, parent_id, type_name, type_desc, order_num, status, create_by, create_time, update_by, update_time, remark from em_meeting_type
    </sql>

    <select id="selectEmMeetingTypeList" parameterType="EmMeetingType" resultMap="EmMeetingTypeResult">
        <include refid="selectEmMeetingTypeVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="typeDesc != null  and typeDesc != ''"> and type_desc = #{typeDesc}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by order_num
    </select>
    
    <select id="selectEmMeetingTypeByTypeId" parameterType="Long" resultMap="EmMeetingTypeResult">
        <include refid="selectEmMeetingTypeVo"/>
        where type_id = #{typeId}
    </select>
        
    <insert id="insertEmMeetingType" parameterType="EmMeetingType" useGeneratedKeys="true" keyProperty="typeId">
        insert into em_meeting_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="typeDesc != null">type_desc,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="typeDesc != null">#{typeDesc},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmMeetingType" parameterType="EmMeetingType">
        update em_meeting_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="typeDesc != null">type_desc = #{typeDesc},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where type_id = #{typeId}
    </update>

    <delete id="deleteEmMeetingTypeByTypeId" parameterType="Long">
        delete from em_meeting_type where type_id = #{typeId}
    </delete>

    <delete id="deleteEmMeetingTypeByTypeIds" parameterType="String">
        delete from em_meeting_type where type_id in 
        <foreach item="typeId" collection="array" open="(" separator="," close=")">
            #{typeId}
        </foreach>
    </delete>

    <select id="selectChildrenCount" parameterType="Long" resultType="int">
        select count(1) from em_meeting_type where parent_id = #{parentId}
    </select>

</mapper>
