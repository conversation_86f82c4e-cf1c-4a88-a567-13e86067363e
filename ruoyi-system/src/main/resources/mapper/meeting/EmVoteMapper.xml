<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmVoteMapper">
    
    <resultMap type="EmVote" id="EmVoteResult">
        <result property="voteId"    column="vote_id"    />
        <result property="voteTitle"    column="vote_title"    />
        <result property="voteContent"    column="vote_content"    />
        <result property="subMeetingId"    column="sub_meeting_id"    />
        <result property="subMeetingName"    column="sub_meeting_name"    />
        <result property="agendaId"    column="agenda_id"    />
        <result property="agendaTitle"    column="agenda_title"    />
        <result property="voteType"    column="vote_type"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmVoteVo">
        select v.vote_id, v.vote_title, v.vote_content, v.sub_meeting_id, s.sub_meeting_name,
               v.agenda_id, a.agenda_title, v.vote_type, v.status, v.create_by, v.create_time, 
               v.update_by, v.update_time, v.remark 
        from em_vote v
        left join em_sub_meeting s on v.sub_meeting_id = s.sub_meeting_id
        left join em_agenda a on v.agenda_id = a.agenda_id
    </sql>

    <select id="selectEmVoteList" parameterType="EmVote" resultMap="EmVoteResult">
        <include refid="selectEmVoteVo"/>
        <where>  
            <if test="voteTitle != null  and voteTitle != ''"> and v.vote_title like concat('%', #{voteTitle}, '%')</if>
            <if test="subMeetingId != null "> and v.sub_meeting_id = #{subMeetingId}</if>
            <if test="agendaId != null "> and v.agenda_id = #{agendaId}</if>
            <if test="voteType != null  and voteType != ''"> and v.vote_type = #{voteType}</if>
            <if test="status != null  and status != ''"> and v.status = #{status}</if>
        </where>
        order by v.create_time desc
    </select>
    
    <select id="selectEmVoteByVoteId" parameterType="Long" resultMap="EmVoteResult">
        <include refid="selectEmVoteVo"/>
        where v.vote_id = #{voteId}
    </select>
        
    <insert id="insertEmVote" parameterType="EmVote" useGeneratedKeys="true" keyProperty="voteId">
        insert into em_vote
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="voteTitle != null and voteTitle != ''">vote_title,</if>
            <if test="voteContent != null">vote_content,</if>
            <if test="subMeetingId != null">sub_meeting_id,</if>
            <if test="agendaId != null">agenda_id,</if>
            <if test="voteType != null">vote_type,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="voteTitle != null and voteTitle != ''">#{voteTitle},</if>
            <if test="voteContent != null">#{voteContent},</if>
            <if test="subMeetingId != null">#{subMeetingId},</if>
            <if test="agendaId != null">#{agendaId},</if>
            <if test="voteType != null">#{voteType},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmVote" parameterType="EmVote">
        update em_vote
        <trim prefix="SET" suffixOverrides=",">
            <if test="voteTitle != null and voteTitle != ''">vote_title = #{voteTitle},</if>
            <if test="voteContent != null">vote_content = #{voteContent},</if>
            <if test="subMeetingId != null">sub_meeting_id = #{subMeetingId},</if>
            <if test="agendaId != null">agenda_id = #{agendaId},</if>
            <if test="voteType != null">vote_type = #{voteType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where vote_id = #{voteId}
    </update>

    <delete id="deleteEmVoteByVoteId" parameterType="Long">
        delete from em_vote where vote_id = #{voteId}
    </delete>

    <delete id="deleteEmVoteByVoteIds" parameterType="String">
        delete from em_vote where vote_id in 
        <foreach item="voteId" collection="array" open="(" separator="," close=")">
            #{voteId}
        </foreach>
    </delete>

    <update id="updateVoteStatus">
        update em_vote set status = #{status} where vote_id = #{voteId}
    </update>

</mapper>
