<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmNoticeMapper">
    
    <resultMap type="EmNotice" id="EmNoticeResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="attachmentName"    column="attachment_name"    />
        <result property="attachmentPath"    column="attachment_path"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmNoticeVo">
        select notice_id, notice_title, notice_content, notice_type, attachment_name, 
               attachment_path, status, create_by, create_time, update_by, update_time, remark 
        from em_notice
    </sql>

    <select id="selectEmNoticeList" parameterType="EmNotice" resultMap="EmNoticeResult">
        <include refid="selectEmNoticeVo"/>
        <where>  
            <if test="noticeTitle != null  and noticeTitle != ''"> and notice_title like concat('%', #{noticeTitle}, '%')</if>
            <if test="noticeType != null  and noticeType != ''"> and notice_type = #{noticeType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectEmNoticeByNoticeId" parameterType="Long" resultMap="EmNoticeResult">
        <include refid="selectEmNoticeVo"/>
        where notice_id = #{noticeId}
    </select>
        
    <insert id="insertEmNotice" parameterType="EmNotice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into em_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">notice_title,</if>
            <if test="noticeContent != null">notice_content,</if>
            <if test="noticeType != null">notice_type,</if>
            <if test="attachmentName != null">attachment_name,</if>
            <if test="attachmentPath != null">attachment_path,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle},</if>
            <if test="noticeContent != null">#{noticeContent},</if>
            <if test="noticeType != null">#{noticeType},</if>
            <if test="attachmentName != null">#{attachmentName},</if>
            <if test="attachmentPath != null">#{attachmentPath},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmNotice" parameterType="EmNotice">
        update em_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle},</if>
            <if test="noticeContent != null">notice_content = #{noticeContent},</if>
            <if test="noticeType != null">notice_type = #{noticeType},</if>
            <if test="attachmentName != null">attachment_name = #{attachmentName},</if>
            <if test="attachmentPath != null">attachment_path = #{attachmentPath},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteEmNoticeByNoticeId" parameterType="Long">
        delete from em_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteEmNoticeByNoticeIds" parameterType="String">
        delete from em_notice where notice_id in 
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

</mapper>
