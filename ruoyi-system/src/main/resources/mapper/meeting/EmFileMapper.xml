<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmFileMapper">
    
    <resultMap type="EmFile" id="EmFileResult">
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="originalName"    column="original_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileExt"    column="file_ext"    />
        <result property="agendaId"    column="agenda_id"    />
        <result property="agendaTitle"    column="agenda_title"    />
        <result property="meetingId"    column="meeting_id"    />
        <result property="meetingName"    column="meeting_name"    />
        <result property="isPassword"    column="is_password"    />
        <result property="password"    column="password"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmFileVo">
        select f.file_id, f.file_name, f.original_name, f.file_path, f.file_size, 
               f.file_type, f.file_ext, f.agenda_id, a.agenda_title, f.meeting_id, 
               m.meeting_name, f.is_password, f.password, f.order_num, f.status, 
               f.create_by, f.create_time, f.update_by, f.update_time, f.remark 
        from em_file f
        left join em_agenda a on f.agenda_id = a.agenda_id
        left join em_meeting m on f.meeting_id = m.meeting_id
    </sql>

    <select id="selectEmFileList" parameterType="EmFile" resultMap="EmFileResult">
        <include refid="selectEmFileVo"/>
        <where>  
            <if test="fileName != null  and fileName != ''"> and f.file_name like concat('%', #{fileName}, '%')</if>
            <if test="agendaId != null "> and f.agenda_id = #{agendaId}</if>
            <if test="meetingId != null "> and f.meeting_id = #{meetingId}</if>
            <if test="fileType != null  and fileType != ''"> and f.file_type = #{fileType}</if>
            <if test="status != null  and status != ''"> and f.status = #{status}</if>
        </where>
        order by f.order_num, f.create_time desc
    </select>
    
    <select id="selectEmFileByFileId" parameterType="Long" resultMap="EmFileResult">
        <include refid="selectEmFileVo"/>
        where f.file_id = #{fileId}
    </select>

    <select id="selectEmFileByAgendaId" parameterType="Long" resultMap="EmFileResult">
        <include refid="selectEmFileVo"/>
        where f.agenda_id = #{agendaId}
        order by f.order_num, f.create_time desc
    </select>

    <select id="selectEmFileByMeetingId" parameterType="Long" resultMap="EmFileResult">
        <include refid="selectEmFileVo"/>
        where f.meeting_id = #{meetingId}
        order by f.order_num, f.create_time desc
    </select>
        
    <insert id="insertEmFile" parameterType="EmFile" useGeneratedKeys="true" keyProperty="fileId">
        insert into em_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalName != null">original_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileExt != null">file_ext,</if>
            <if test="agendaId != null">agenda_id,</if>
            <if test="meetingId != null">meeting_id,</if>
            <if test="isPassword != null">is_password,</if>
            <if test="password != null">password,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalName != null">#{originalName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileExt != null">#{fileExt},</if>
            <if test="agendaId != null">#{agendaId},</if>
            <if test="meetingId != null">#{meetingId},</if>
            <if test="isPassword != null">#{isPassword},</if>
            <if test="password != null">#{password},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmFile" parameterType="EmFile">
        update em_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalName != null">original_name = #{originalName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileExt != null">file_ext = #{fileExt},</if>
            <if test="agendaId != null">agenda_id = #{agendaId},</if>
            <if test="meetingId != null">meeting_id = #{meetingId},</if>
            <if test="isPassword != null">is_password = #{isPassword},</if>
            <if test="password != null">password = #{password},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where file_id = #{fileId}
    </update>

    <delete id="deleteEmFileByFileId" parameterType="Long">
        delete from em_file where file_id = #{fileId}
    </delete>

    <delete id="deleteEmFileByFileIds" parameterType="String">
        delete from em_file where file_id in 
        <foreach item="fileId" collection="array" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>

    <update id="bindAgenda">
        update em_file set agenda_id = #{agendaId} where file_id = #{fileId}
    </update>

    <update id="updateFileOrder">
        update em_file set order_num = #{orderNum} where file_id = #{fileId}
    </update>

</mapper>
