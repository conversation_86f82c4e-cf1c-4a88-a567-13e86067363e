<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmSubMeetingMapper">
    
    <resultMap type="EmSubMeeting" id="EmSubMeetingResult">
        <result property="subMeetingId"    column="sub_meeting_id"    />
        <result property="parentMeetingId"    column="parent_meeting_id"    />
        <result property="subMeetingName"    column="sub_meeting_name"    />
        <result property="subMeetingDesc"    column="sub_meeting_desc"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="location"    column="location"    />
        <result property="organizer"    column="organizer"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmSubMeetingVo">
        select sub_meeting_id, parent_meeting_id, sub_meeting_name, sub_meeting_desc, start_time, end_time, location, organizer, status, create_by, create_time, update_by, update_time, remark from em_sub_meeting
    </sql>

    <select id="selectEmSubMeetingList" parameterType="EmSubMeeting" resultMap="EmSubMeetingResult">
        <include refid="selectEmSubMeetingVo"/>
        <where>  
            <if test="parentMeetingId != null "> and parent_meeting_id = #{parentMeetingId}</if>
            <if test="subMeetingName != null  and subMeetingName != ''"> and sub_meeting_name like concat('%', #{subMeetingName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEmSubMeetingBySubMeetingId" parameterType="Long" resultMap="EmSubMeetingResult">
        <include refid="selectEmSubMeetingVo"/>
        where sub_meeting_id = #{subMeetingId}
    </select>

    <select id="selectEmSubMeetingByParentId" parameterType="Long" resultMap="EmSubMeetingResult">
        <include refid="selectEmSubMeetingVo"/>
        where parent_meeting_id = #{parentMeetingId}
    </select>
        
    <insert id="insertEmSubMeeting" parameterType="EmSubMeeting" useGeneratedKeys="true" keyProperty="subMeetingId">
        insert into em_sub_meeting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentMeetingId != null">parent_meeting_id,</if>
            <if test="subMeetingName != null and subMeetingName != ''">sub_meeting_name,</if>
            <if test="subMeetingDesc != null">sub_meeting_desc,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="location != null">location,</if>
            <if test="organizer != null">organizer,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentMeetingId != null">#{parentMeetingId},</if>
            <if test="subMeetingName != null and subMeetingName != ''">#{subMeetingName},</if>
            <if test="subMeetingDesc != null">#{subMeetingDesc},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="location != null">#{location},</if>
            <if test="organizer != null">#{organizer},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmSubMeeting" parameterType="EmSubMeeting">
        update em_sub_meeting
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentMeetingId != null">parent_meeting_id = #{parentMeetingId},</if>
            <if test="subMeetingName != null and subMeetingName != ''">sub_meeting_name = #{subMeetingName},</if>
            <if test="subMeetingDesc != null">sub_meeting_desc = #{subMeetingDesc},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="location != null">location = #{location},</if>
            <if test="organizer != null">organizer = #{organizer},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where sub_meeting_id = #{subMeetingId}
    </update>

    <delete id="deleteEmSubMeetingBySubMeetingId" parameterType="Long">
        delete from em_sub_meeting where sub_meeting_id = #{subMeetingId}
    </delete>

    <delete id="deleteEmSubMeetingBySubMeetingIds" parameterType="String">
        delete from em_sub_meeting where sub_meeting_id in 
        <foreach item="subMeetingId" collection="array" open="(" separator="," close=")">
            #{subMeetingId}
        </foreach>
    </delete>

</mapper>
