<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmDeviceMapper">
    
    <resultMap type="EmDevice" id="EmDeviceResult">
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceModel"    column="device_model"    />
        <result property="macAddress"    column="mac_address"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="location"    column="location"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmDeviceVo">
        select device_id, device_name, device_code, device_type, device_model, 
               mac_address, ip_address, location, status, create_by, create_time, 
               update_by, update_time, remark 
        from em_device
    </sql>

    <select id="selectEmDeviceList" parameterType="EmDevice" resultMap="EmDeviceResult">
        <include refid="selectEmDeviceVo"/>
        <where>  
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="macAddress != null  and macAddress != ''"> and mac_address = #{macAddress}</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            <if test="location != null  and location != ''"> and location like concat('%', #{location}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectEmDeviceByDeviceId" parameterType="Long" resultMap="EmDeviceResult">
        <include refid="selectEmDeviceVo"/>
        where device_id = #{deviceId}
    </select>
        
    <insert id="insertEmDevice" parameterType="EmDevice" useGeneratedKeys="true" keyProperty="deviceId">
        insert into em_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="deviceModel != null">device_model,</if>
            <if test="macAddress != null">mac_address,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="location != null">location,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="deviceModel != null">#{deviceModel},</if>
            <if test="macAddress != null">#{macAddress},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="location != null">#{location},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmDevice" parameterType="EmDevice">
        update em_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceModel != null">device_model = #{deviceModel},</if>
            <if test="macAddress != null">mac_address = #{macAddress},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="location != null">location = #{location},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <delete id="deleteEmDeviceByDeviceId" parameterType="Long">
        delete from em_device where device_id = #{deviceId}
    </delete>

    <delete id="deleteEmDeviceByDeviceIds" parameterType="String">
        delete from em_device where device_id in 
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>

    <update id="updateDeviceStatus">
        update em_device set status = #{status} where device_id = #{deviceId}
    </update>

</mapper>
