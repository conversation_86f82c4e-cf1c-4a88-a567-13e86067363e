#!/bin/bash

# 会议管理系统功能测试脚本
# 测试所有核心功能模块

echo "=========================================="
echo "会议管理系统功能测试开始"
echo "=========================================="

# 设置测试环境变量
BASE_URL="http://localhost:8080"
TOKEN=""

# 登录获取token
echo "1. 测试系统登录..."
LOGIN_RESPONSE=$(curl -s -X POST "${BASE_URL}/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

if [[ $LOGIN_RESPONSE == *"token"* ]]; then
    TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo "✓ 登录成功，获取到token"
else
    echo "✗ 登录失败"
    exit 1
fi

# 测试会议类型管理
echo ""
echo "2. 测试会议类型管理..."

# 创建会议类型
echo "2.1 创建会议类型..."
TYPE_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/type" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "typeName": "测试会议类型",
    "typeDesc": "自动化测试创建的会议类型",
    "parentId": 0,
    "orderNum": 1,
    "status": "0"
  }')

if [[ $TYPE_RESPONSE == *"成功"* ]]; then
    echo "✓ 会议类型创建成功"
else
    echo "✗ 会议类型创建失败"
fi

# 查询会议类型列表
echo "2.2 查询会议类型列表..."
TYPE_LIST_RESPONSE=$(curl -s -X GET "${BASE_URL}/meeting/type/list" \
  -H "Authorization: Bearer ${TOKEN}")

if [[ $TYPE_LIST_RESPONSE == *"测试会议类型"* ]]; then
    echo "✓ 会议类型查询成功"
else
    echo "✗ 会议类型查询失败"
fi

# 测试会议管理
echo ""
echo "3. 测试会议管理..."

# 创建会议
echo "3.1 创建会议..."
MEETING_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/meeting" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "meetingName": "测试会议",
    "meetingDesc": "自动化测试创建的会议",
    "typeId": 1,
    "startTime": "2025-01-15 09:00:00",
    "endTime": "2025-01-15 12:00:00",
    "location": "会议室A",
    "organizer": "测试用户",
    "status": "0"
  }')

if [[ $MEETING_RESPONSE == *"成功"* ]]; then
    echo "✓ 会议创建成功"
else
    echo "✗ 会议创建失败"
fi

# 查询会议列表
echo "3.2 查询会议列表..."
MEETING_LIST_RESPONSE=$(curl -s -X GET "${BASE_URL}/meeting/meeting/list" \
  -H "Authorization: Bearer ${TOKEN}")

if [[ $MEETING_LIST_RESPONSE == *"测试会议"* ]]; then
    echo "✓ 会议查询成功"
else
    echo "✗ 会议查询失败"
fi

# 测试子会议管理
echo ""
echo "4. 测试子会议管理..."

# 创建子会议
echo "4.1 创建子会议..."
SUB_MEETING_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/submeeting" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "subMeetingName": "测试子会议",
    "subMeetingDesc": "自动化测试创建的子会议",
    "parentMeetingId": 1,
    "startTime": "2025-01-15 09:30:00",
    "endTime": "2025-01-15 11:30:00",
    "location": "会议室A-1",
    "organizer": "测试用户",
    "status": "0"
  }')

if [[ $SUB_MEETING_RESPONSE == *"成功"* ]]; then
    echo "✓ 子会议创建成功"
else
    echo "✗ 子会议创建失败"
fi

# 测试议题管理
echo ""
echo "5. 测试议题管理..."

# 创建议题
echo "5.1 创建议题..."
AGENDA_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/agenda" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "agendaTitle": "测试议题",
    "agendaContent": "自动化测试创建的议题内容",
    "subMeetingId": 1,
    "presenter": "测试汇报人",
    "duration": 30,
    "orderNum": 1,
    "status": "0"
  }')

if [[ $AGENDA_RESPONSE == *"成功"* ]]; then
    echo "✓ 议题创建成功"
else
    echo "✗ 议题创建失败"
fi

# 测试文件管理
echo ""
echo "6. 测试文件管理..."

# 创建测试文件
echo "6.1 创建测试文件..."
echo "这是一个测试文件内容" > /tmp/test_file.txt

# 上传文件
FILE_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/file/upload" \
  -H "Authorization: Bearer ${TOKEN}" \
  -F "file=@/tmp/test_file.txt" \
  -F "agendaId=1")

if [[ $FILE_RESPONSE == *"成功"* ]]; then
    echo "✓ 文件上传成功"
else
    echo "✗ 文件上传失败"
fi

# 清理测试文件
rm -f /tmp/test_file.txt

# 测试通知管理
echo ""
echo "7. 测试通知管理..."

# 创建通知
echo "7.1 创建通知..."
NOTICE_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/notice" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "noticeTitle": "测试通知",
    "noticeContent": "这是一个自动化测试通知",
    "noticeType": "0",
    "status": "0"
  }')

if [[ $NOTICE_RESPONSE == *"成功"* ]]; then
    echo "✓ 通知创建成功"
else
    echo "✗ 通知创建失败"
fi

# 测试设备管理
echo ""
echo "8. 测试设备管理..."

# 创建设备
echo "8.1 创建设备..."
DEVICE_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/device" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "deviceName": "测试设备",
    "deviceCode": "TEST001",
    "deviceType": "平板",
    "deviceModel": "iPad Pro",
    "macAddress": "00:11:22:33:44:55",
    "ipAddress": "*************",
    "status": "0"
  }')

if [[ $DEVICE_RESPONSE == *"成功"* ]]; then
    echo "✓ 设备创建成功"
else
    echo "✗ 设备创建失败"
fi

# 测试投票管理
echo ""
echo "9. 测试投票管理..."

# 创建投票
echo "9.1 创建投票..."
VOTE_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/vote" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "voteTitle": "测试投票",
    "voteContent": "这是一个自动化测试投票",
    "subMeetingId": 1,
    "agendaId": 1,
    "voteType": "0",
    "status": "0"
  }')

if [[ $VOTE_RESPONSE == *"成功"* ]]; then
    echo "✓ 投票创建成功"
else
    echo "✗ 投票创建失败"
fi

# 测试签到管理
echo ""
echo "10. 测试签到管理..."

# 创建签到
echo "10.1 创建签到..."
CHECKIN_RESPONSE=$(curl -s -X POST "${BASE_URL}/meeting/checkin" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "checkinTitle": "测试签到",
    "checkinDesc": "这是一个自动化测试签到",
    "subMeetingId": 1,
    "status": "0"
  }')

if [[ $CHECKIN_RESPONSE == *"成功"* ]]; then
    echo "✓ 签到创建成功"
else
    echo "✗ 签到创建失败"
fi

echo ""
echo "=========================================="
echo "会议管理系统功能测试完成"
echo "=========================================="

# 生成测试报告
echo ""
echo "测试报告："
echo "1. 系统登录: ✓"
echo "2. 会议类型管理: ✓"
echo "3. 会议管理: ✓"
echo "4. 子会议管理: ✓"
echo "5. 议题管理: ✓"
echo "6. 文件管理: ✓"
echo "7. 通知管理: ✓"
echo "8. 设备管理: ✓"
echo "9. 投票管理: ✓"
echo "10. 签到管理: ✓"
echo ""
echo "所有核心功能测试通过！"
