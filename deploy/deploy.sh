#!/bin/bash

# 会议管理系统部署脚本
# 支持开发环境、测试环境、生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "会议管理系统部署脚本"
    echo ""
    echo "用法: $0 [选项] [环境]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境"
    echo "  test    测试环境"
    echo "  prod    生产环境"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -v, --version  显示版本信息"
    echo "  -c, --clean    清理构建缓存"
    echo "  -d, --db       初始化数据库"
    echo "  -f, --frontend 只部署前端"
    echo "  -b, --backend  只部署后端"
    echo ""
    echo "示例:"
    echo "  $0 dev              # 部署到开发环境"
    echo "  $0 prod -d          # 部署到生产环境并初始化数据库"
    echo "  $0 test -f          # 只部署前端到测试环境"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "检查系统依赖..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        print_message $RED "错误: 未找到Java，请安装JDK 8或更高版本"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        print_message $RED "错误: 未找到Maven，请安装Maven 3.6或更高版本"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_message $RED "错误: 未找到Node.js，请安装Node.js 14或更高版本"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        print_message $RED "错误: 未找到npm，请安装npm"
        exit 1
    fi
    
    print_message $GREEN "✓ 所有依赖检查通过"
}

# 设置环境变量
setup_environment() {
    local env=$1
    
    print_message $BLUE "设置${env}环境变量..."
    
    case $env in
        "dev")
            export SPRING_PROFILES_ACTIVE=dev
            export SERVER_PORT=8080
            export DB_HOST=localhost
            export DB_PORT=3306
            export DB_NAME=emeeting_dev
            export REDIS_HOST=localhost
            export REDIS_PORT=6379
            ;;
        "test")
            export SPRING_PROFILES_ACTIVE=test
            export SERVER_PORT=8081
            export DB_HOST=test-db-server
            export DB_PORT=3306
            export DB_NAME=emeeting_test
            export REDIS_HOST=test-redis-server
            export REDIS_PORT=6379
            ;;
        "prod")
            export SPRING_PROFILES_ACTIVE=prod
            export SERVER_PORT=8080
            export DB_HOST=prod-db-server
            export DB_PORT=3306
            export DB_NAME=emeeting_prod
            export REDIS_HOST=prod-redis-server
            export REDIS_PORT=6379
            ;;
        *)
            print_message $RED "错误: 不支持的环境 $env"
            exit 1
            ;;
    esac
    
    print_message $GREEN "✓ 环境变量设置完成"
}

# 清理构建缓存
clean_build() {
    print_message $BLUE "清理构建缓存..."
    
    # 清理Maven缓存
    mvn clean
    
    # 清理前端缓存
    if [ -d "ruoyi-ui/node_modules" ]; then
        rm -rf ruoyi-ui/node_modules
    fi
    
    if [ -d "ruoyi-ui/dist" ]; then
        rm -rf ruoyi-ui/dist
    fi
    
    print_message $GREEN "✓ 构建缓存清理完成"
}

# 初始化数据库
init_database() {
    print_message $BLUE "初始化数据库..."
    
    # 检查MySQL连接
    if ! command -v mysql &> /dev/null; then
        print_message $YELLOW "警告: 未找到mysql命令，请手动执行SQL脚本"
        print_message $YELLOW "SQL脚本位置: sql/ry_20250522.sql"
        return
    fi
    
    # 执行SQL脚本
    mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER:-root} -p${DB_PASSWORD} ${DB_NAME} < sql/ry_20250522.sql
    
    print_message $GREEN "✓ 数据库初始化完成"
}

# 构建后端
build_backend() {
    print_message $BLUE "构建后端应用..."
    
    # Maven打包
    mvn clean package -Dmaven.test.skip=true
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 后端构建成功"
    else
        print_message $RED "✗ 后端构建失败"
        exit 1
    fi
}

# 构建前端
build_frontend() {
    print_message $BLUE "构建前端应用..."
    
    cd ruoyi-ui
    
    # 安装依赖
    npm install
    
    # 构建生产版本
    npm run build:prod
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 前端构建成功"
    else
        print_message $RED "✗ 前端构建失败"
        exit 1
    fi
    
    cd ..
}

# 部署后端
deploy_backend() {
    local env=$1
    
    print_message $BLUE "部署后端到${env}环境..."
    
    # 停止现有服务
    if pgrep -f "ruoyi-admin" > /dev/null; then
        print_message $YELLOW "停止现有后端服务..."
        pkill -f "ruoyi-admin"
        sleep 5
    fi
    
    # 创建部署目录
    mkdir -p deploy/${env}/backend
    
    # 复制JAR文件
    cp ruoyi-admin/target/ruoyi-admin.jar deploy/${env}/backend/
    
    # 创建启动脚本
    cat > deploy/${env}/backend/start.sh << EOF
#!/bin/bash
export SPRING_PROFILES_ACTIVE=${env}
nohup java -jar ruoyi-admin.jar > app.log 2>&1 &
echo \$! > app.pid
EOF
    
    chmod +x deploy/${env}/backend/start.sh
    
    # 启动服务
    cd deploy/${env}/backend
    ./start.sh
    cd ../../..
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    if curl -f http://localhost:${SERVER_PORT}/actuator/health > /dev/null 2>&1; then
        print_message $GREEN "✓ 后端服务启动成功"
    else
        print_message $RED "✗ 后端服务启动失败"
        exit 1
    fi
}

# 部署前端
deploy_frontend() {
    local env=$1
    
    print_message $BLUE "部署前端到${env}环境..."
    
    # 创建部署目录
    mkdir -p deploy/${env}/frontend
    
    # 复制构建文件
    cp -r ruoyi-ui/dist/* deploy/${env}/frontend/
    
    # 创建Nginx配置
    cat > deploy/${env}/frontend/nginx.conf << EOF
server {
    listen 80;
    server_name localhost;
    
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files \$uri \$uri/ /index.html;
    }
    
    location /prod-api/ {
        proxy_pass http://localhost:${SERVER_PORT}/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    print_message $GREEN "✓ 前端部署完成"
    print_message $YELLOW "注意: 请将 deploy/${env}/frontend/ 目录部署到Web服务器"
}

# 健康检查
health_check() {
    print_message $BLUE "执行健康检查..."
    
    # 检查后端服务
    if curl -f http://localhost:${SERVER_PORT}/actuator/health > /dev/null 2>&1; then
        print_message $GREEN "✓ 后端服务运行正常"
    else
        print_message $RED "✗ 后端服务异常"
    fi
    
    # 检查数据库连接
    if curl -f http://localhost:${SERVER_PORT}/actuator/health/db > /dev/null 2>&1; then
        print_message $GREEN "✓ 数据库连接正常"
    else
        print_message $YELLOW "⚠ 数据库连接异常"
    fi
    
    # 检查Redis连接
    if curl -f http://localhost:${SERVER_PORT}/actuator/health/redis > /dev/null 2>&1; then
        print_message $GREEN "✓ Redis连接正常"
    else
        print_message $YELLOW "⚠ Redis连接异常"
    fi
}

# 主函数
main() {
    local environment=""
    local clean_flag=false
    local db_flag=false
    local frontend_only=false
    local backend_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--version)
                echo "会议管理系统部署脚本 v1.0.0"
                exit 0
                ;;
            -c|--clean)
                clean_flag=true
                shift
                ;;
            -d|--db)
                db_flag=true
                shift
                ;;
            -f|--frontend)
                frontend_only=true
                shift
                ;;
            -b|--backend)
                backend_only=true
                shift
                ;;
            dev|test|prod)
                environment=$1
                shift
                ;;
            *)
                print_message $RED "错误: 未知参数 $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境参数
    if [ -z "$environment" ]; then
        print_message $RED "错误: 请指定部署环境 (dev/test/prod)"
        show_help
        exit 1
    fi
    
    print_message $GREEN "=========================================="
    print_message $GREEN "会议管理系统部署开始"
    print_message $GREEN "环境: $environment"
    print_message $GREEN "=========================================="
    
    # 执行部署流程
    check_dependencies
    setup_environment $environment
    
    if [ "$clean_flag" = true ]; then
        clean_build
    fi
    
    if [ "$db_flag" = true ]; then
        init_database
    fi
    
    if [ "$frontend_only" = false ]; then
        build_backend
        deploy_backend $environment
    fi
    
    if [ "$backend_only" = false ]; then
        build_frontend
        deploy_frontend $environment
    fi
    
    health_check
    
    print_message $GREEN "=========================================="
    print_message $GREEN "会议管理系统部署完成"
    print_message $GREEN "=========================================="
    
    # 显示访问信息
    print_message $BLUE "访问信息:"
    print_message $BLUE "后端服务: http://localhost:${SERVER_PORT}"
    print_message $BLUE "前端应用: 请配置Web服务器指向 deploy/${environment}/frontend/"
    print_message $BLUE "管理员账号: admin / admin123"
}

# 执行主函数
main "$@"
